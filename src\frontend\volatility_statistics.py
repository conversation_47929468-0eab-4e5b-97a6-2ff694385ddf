"""
Volatility Statistics Tab
Placeholder for future volatility analysis functionality.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTabWidget, QSplitter, QRadioButton, QButtonGroup, QPushButton, QTableView, QAbstractItemView, QStackedWidget, QLineEdit, QComboBox, QCheckBox, QDialog, QTextEdit
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor
from typing import Dict, Any
import pyqtgraph as pg
import numpy as np
from scipy.interpolate import CubicSpline, splrep, splev

# Import the table model from data_tab
from src.frontend.data_tab import MarketDataTableModel


class VolatilityChart(pg.PlotWidget):
    """Empty Volatility Graph chart widget."""

    def __init__(self):
        super().__init__()
        self.setBackground('#1e1e1e')
        self.showGrid(x=False, y=False)  # Turn off grid

        # Remove X and Y axes
        self.hideAxis('left')
        self.hideAxis('bottom')

        # Removed vertical reference lines as requested

    def calculate_arrow_sizes(self, projected_highs, projected_lows):
        """Calculate dynamic arrow sizes based on projected high/low range.

        Returns:
            tuple: (arrow_y_size, arrow_x_size) where:
                - arrow_y_size is 3% of the height range (highest high to lowest low)
                - arrow_x_size is 0.5% of the visual box range
        """
        try:
            if not projected_highs or not projected_lows:
                return 1.5, 0.05  # Fallback to fixed sizes

            # Calculate the full range from highest projected high to lowest projected low
            highest_high = max(projected_highs)
            lowest_low = min(projected_lows)
            total_range = highest_high - lowest_low

            # Arrow height: 3% of the total price range
            arrow_y_size = total_range * 0.03

            # Arrow width: 0.5% of visual box range (VolatilityChart: -4x to 4x = 8 units)
            arrow_x_size = 8 * 0.005  # 0.5% of 8 = 0.08

            return arrow_y_size, arrow_x_size

        except Exception as e:
            print(f"Error calculating arrow sizes: {e}")
            import traceback
            traceback.print_exc()
            return 1.5, 0.05  # Fallback to fixed sizes

    def update_data(self, filtered_high_data, filtered_low_data, market_data=None):
        """Update volatility chart with filtered data and market data from backend."""
        # Clear existing plot items
        self.clear()

        if not market_data:
            return

        # Get the data service from the parent tab to perform calculations
        parent_tab = self.parent()
        while parent_tab and not hasattr(parent_tab, 'data_service'):
            parent_tab = parent_tab.parent()

        if not parent_tab or not hasattr(parent_tab, 'data_service') or not parent_tab.data_service:
            print("No data service available for volatility calculations")
            return

        # Use backend service to calculate all data
        calc_data = parent_tab.data_service.calculate_volatility_data(
            filtered_high_data, filtered_low_data, market_data
        )

        if not calc_data:
            print("Failed to calculate volatility data")
            return

        # Extract calculated values
        title_info = calc_data.get('title_info', {})
        current_close = calc_data.get('current_close')
        max_high = calc_data.get('max_high')
        max_low = calc_data.get('max_low')
        apex = calc_data.get('apex')
        apex_high_mean = calc_data.get('apex_high_mean')
        apex_low_mean = calc_data.get('apex_low_mean')
        highs_stats = calc_data.get('highs_stats', {})
        lows_stats = calc_data.get('lows_stats', {})
        all_highs = calc_data.get('all_highs', [])
        all_lows = calc_data.get('all_lows', [])
        arrow_y_size, arrow_x_size = calc_data.get('arrow_sizes', (1.5, 0.05))
        axis_limits = calc_data.get('axis_limits', {})
        title_position = calc_data.get('title_position', {})

        # Add title to top left
        try:
            if title_info:
                title_text = title_info.get('title_text', '')
                title_label = pg.TextItem(title_text, color='white', anchor=(0, 0))
                title_label.setFont(QFont("Segoe UI", 8))
                title_label.setPos(-3.5, 0)  # Will be repositioned later
                self.addItem(title_label)
                self.title_label = title_label

        except Exception as e:
            print(f"Error adding title to volatility chart: {e}")

        # Add current close price line and label
        try:
            if current_close is not None:
                # Add a finite grey 2pt line from -1 to 1 at the close price level
                self.plot(
                    [-1, 1], [current_close, current_close],
                    pen=pg.mkPen(color='grey', width=2),
                    name=f'Close: {current_close:.2f}'
                )

                # Add label for Current Price - above the line, left aligned
                current_price_label = pg.TextItem(f'Current Price:\n${current_close:.2f}', color='white', anchor=(0, 1))
                current_price_label.setPos(-1, current_close)  # Directly above the line
                self.addItem(current_price_label)

        except Exception as e:
            print(f"Error adding close price line to volatility chart: {e}")

        # All calculations are now done by the backend service
        # The calculated values are already extracted above

        # Add Max_High dot at 3x position (white) with label
        try:
            if max_high is not None:
                self.plot(
                    [3], [max_high],
                    pen=None,
                    symbol='o',
                    symbolBrush='white',
                    symbolSize=8,
                    name=f'Max_High: {max_high:.2f}'
                )

                # Add label for Max_High
                max_high_label = pg.TextItem(f'Max: ${max_high:.2f}', color='white', anchor=(0, 0.5))
                max_high_label.setPos(3.1, max_high)
                self.addItem(max_high_label)

        except Exception as e:
            print(f"Error adding Max_High dot to volatility chart: {e}")

        # Add Max_Low dot at -3x position (white) with label
        try:
            if max_low is not None:
                self.plot(
                    [-3], [max_low],
                    pen=None,
                    symbol='o',
                    symbolBrush='white',
                    symbolSize=8,
                    name=f'Max_Low: {max_low:.2f}'
                )

                # Add label for Max_Low
                max_low_label = pg.TextItem(f'Max: ${max_low:.2f}', color='white', anchor=(1, 0.5))
                max_low_label.setPos(-3.1, max_low)
                self.addItem(max_low_label)

        except Exception as e:
            print(f"Error adding Max_Low dot to volatility chart: {e}")

        # Add apex dot at 0x position (white)
        try:
            if apex is not None:
                self.plot(
                    [0], [apex],
                    pen=None,
                    symbol='o',
                    symbolBrush='white',
                    symbolSize=8,
                    name=f'Apex: {apex:.2f}'
                )
        except Exception as e:
            print(f"Error adding Apex dot to volatility chart: {e}")

        # Add mean of apex and Max_Low at -1x position (white)
        try:
            if apex_low_mean is not None:
                self.plot(
                    [-1], [apex_low_mean],
                    pen=None,
                    symbol='o',
                    symbolBrush='white',
                    symbolSize=8,
                    name=f'Apex-Low: {apex_low_mean:.2f}'
                )
        except Exception as e:
            print(f"Error adding Apex-Low mean dot to volatility chart: {e}")

        # Add mean of apex and Max_High at 1x position (white)
        try:
            if apex_high_mean is not None:
                self.plot(
                    [1], [apex_high_mean],
                    pen=None,
                    symbol='o',
                    symbolBrush='white',
                    symbolSize=8,
                    name=f'Apex-High: {apex_high_mean:.2f}'
                )
        except Exception as e:
            print(f"Error adding Apex-High mean dot to volatility chart: {e}")

        # Add statistical dots for highs at 3x position
        if highs_stats:

            # MaxAvg: highest 50% highs average
            try:
                if 'max_avg' in highs_stats:
                    self.plot(
                        [3], [highs_stats['max_avg']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'MaxAvg_H: {highs_stats["max_avg"]:.2f}'
                    )

                    # Add label for MaxAvg_H
                    max_avg_high_label = pg.TextItem(f'MaxAvg: ${highs_stats["max_avg"]:.2f}', color='white', anchor=(0, 0.5))
                    max_avg_high_label.setPos(3.1, highs_stats['max_avg'])
                    self.addItem(max_avg_high_label)

                    # Add light blue horizontal line from MaxAvg_H to 0x
                    self.plot(
                        [3, 0], [highs_stats['max_avg'], highs_stats['max_avg']],
                        pen=pg.mkPen(color='lightblue', width=2),  # Changed to 2pt
                        name=f'MaxAvg_H_Line: {highs_stats["max_avg"]:.2f}'
                    )
            except Exception as e:
                print(f"Error adding MaxAvg highs dot: {e}")

            # Average: Average of all highs
            try:
                if 'average' in highs_stats:
                    self.plot(
                        [3], [highs_stats['average']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'Avg_H: {highs_stats["average"]:.2f}'
                    )

                    # Add label for Avg_H
                    avg_high_label = pg.TextItem(f'Avg: ${highs_stats["average"]:.2f}', color='white', anchor=(0, 0.5))
                    avg_high_label.setPos(3.1, highs_stats['average'])
                    self.addItem(avg_high_label)

            except Exception as e:
                print(f"Error adding Average highs dot: {e}")

            # Median: Median of all highs - dark blue horizontal line from 0.5x to 0x
            try:
                if 'median' in highs_stats:
                    self.plot(
                        [0.5, 0], [highs_stats['median'], highs_stats['median']],
                        pen=pg.mkPen(color='darkblue', width=4),  # Quadruple width of white lines
                        name=f'Med_H: {highs_stats["median"]:.2f}'
                    )

                    # Add label for Median High - above the line, centered left
                    median_high_label = pg.TextItem(f'Median:\n${highs_stats["median"]:.2f}', color='white', anchor=(0, 1))
                    median_high_label.setPos(0.25, highs_stats['median'])  # Directly above the line
                    self.addItem(median_high_label)

                    # Add vertical line from median high to current price at 0x
                    if current_close is not None:
                        self.plot(
                            [0, 0], [highs_stats['median'], current_close],
                            pen=pg.mkPen(color='darkblue', width=4),  # Same color and width as horizontal
                            name=f'Med_H_Vertical: {highs_stats["median"]:.2f} to {current_close:.2f}'
                        )
            except Exception as e:
                print(f"Error adding Median highs line: {e}")

            # MinAvg: lowest 50% highs average
            try:
                if 'min_avg' in highs_stats:
                    self.plot(
                        [3], [highs_stats['min_avg']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'MinAvg_H: {highs_stats["min_avg"]:.2f}'
                    )

                    # Add label for MinAvg_H
                    min_avg_high_label = pg.TextItem(f'MinAvg: ${highs_stats["min_avg"]:.2f}', color='white', anchor=(0, 0.5))
                    min_avg_high_label.setPos(3.1, highs_stats['min_avg'])
                    self.addItem(min_avg_high_label)

            except Exception as e:
                print(f"Error adding MinAvg highs dot: {e}")

        # Add statistical dots for lows at -3x position
        if lows_stats:
            # MinAvg: highest 50% average low
            try:
                if 'min_avg' in lows_stats:
                    self.plot(
                        [-3], [lows_stats['min_avg']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'MinAvg_L: {lows_stats["min_avg"]:.2f}'
                    )

                    # Add label for MinAvg_L
                    min_avg_low_label = pg.TextItem(f'MinAvg: ${lows_stats["min_avg"]:.2f}', color='white', anchor=(1, 0.5))
                    min_avg_low_label.setPos(-3.1, lows_stats['min_avg'])
                    self.addItem(min_avg_low_label)

            except Exception as e:
                print(f"Error adding MinAvg lows dot: {e}")

            # Average: Average of all lows
            try:
                if 'average' in lows_stats:
                    self.plot(
                        [-3], [lows_stats['average']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'Avg_L: {lows_stats["average"]:.2f}'
                    )

                    # Add label for Avg_L
                    avg_low_label = pg.TextItem(f'Avg: ${lows_stats["average"]:.2f}', color='white', anchor=(1, 0.5))
                    avg_low_label.setPos(-3.1, lows_stats['average'])
                    self.addItem(avg_low_label)

            except Exception as e:
                print(f"Error adding Average lows dot: {e}")

            # Median: Median of all lows - dark red horizontal line from -0.5x to 0x
            try:
                if 'median' in lows_stats:
                    self.plot(
                        [-0.5, 0], [lows_stats['median'], lows_stats['median']],
                        pen=pg.mkPen(color='darkred', width=4),  # Quadruple width of white lines
                        name=f'Med_L: {lows_stats["median"]:.2f}'
                    )

                    # Add label for Median Low - below the line, left aligned to the left of its line
                    median_low_label = pg.TextItem(f'Median:\n${lows_stats["median"]:.2f}', color='white', anchor=(0, 0))
                    median_low_label.setPos(-0.5, lows_stats['median'])  # At the left end of the line (-0.5x), below
                    self.addItem(median_low_label)

                    # Add vertical line from median low to current price at 0x
                    if current_close is not None:
                        self.plot(
                            [0, 0], [lows_stats['median'], current_close],
                            pen=pg.mkPen(color='darkred', width=4),  # Same color and width as horizontal
                            name=f'Med_L_Vertical: {lows_stats["median"]:.2f} to {current_close:.2f}'
                        )
            except Exception as e:
                print(f"Error adding Median lows line: {e}")

            # MaxAvg: lowest 50% average low
            try:
                if 'max_avg' in lows_stats:
                    self.plot(
                        [-3], [lows_stats['max_avg']],
                        pen=None,
                        symbol='o',
                        symbolBrush='white',
                        symbolSize=8,
                        name=f'MaxAvg_L: {lows_stats["max_avg"]:.2f}'
                    )

                    # Add label for MaxAvg_L
                    max_avg_low_label = pg.TextItem(f'MaxAvg: ${lows_stats["max_avg"]:.2f}', color='white', anchor=(1, 0.5))
                    max_avg_low_label.setPos(-3.1, lows_stats['max_avg'])
                    self.addItem(max_avg_low_label)

                    # Add light red horizontal line from MaxAvg_L to 0x
                    self.plot(
                        [-3, 0], [lows_stats['max_avg'], lows_stats['max_avg']],
                        pen=pg.mkPen(color='lightcoral', width=2),  # Changed to 2pt
                        name=f'MaxAvg_L_Line: {lows_stats["max_avg"]:.2f}'
                    )
            except Exception as e:
                print(f"Error adding MaxAvg lows dot: {e}")

        # Add connecting lines
        try:
            # Connect apex to apex_high
            if apex is not None and apex_high_mean is not None:
                self.plot(
                    [0, 1], [apex, apex_high_mean],
                    pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                    name='Apex-ApexHigh Line'
                )

            # Connect apex to apex_low
            if apex is not None and apex_low_mean is not None:
                self.plot(
                    [0, -1], [apex, apex_low_mean],
                    pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                    name='Apex-ApexLow Line'
                )

            # Connect apex_high to all dots on 3x except median
            if apex_high_mean is not None and max_high is not None and highs_stats:
                # Connect to Max_High
                if max_high is not None:
                    self.plot(
                        [1, 3], [apex_high_mean, max_high],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexHigh-MaxHigh Line'
                    )

                # Connect to MaxAvg_H (highest 50% highs average)
                if 'max_avg' in highs_stats:
                    self.plot(
                        [1, 3], [apex_high_mean, highs_stats['max_avg']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexHigh-MaxAvgH Line'
                    )

                # Connect to Avg_H (average of all highs)
                if 'average' in highs_stats:
                    self.plot(
                        [1, 3], [apex_high_mean, highs_stats['average']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexHigh-AvgH Line'
                    )

                # Connect to MinAvg_H (lowest 50% highs average)
                if 'min_avg' in highs_stats:
                    self.plot(
                        [1, 3], [apex_high_mean, highs_stats['min_avg']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexHigh-MinAvgH Line'
                    )

                # Note: Not connecting to median as requested

        except Exception as e:
            print(f"Error adding apex_high connecting lines: {e}")

        try:
            # Connect apex_low to all dots on -3x except median
            if apex_low_mean is not None and max_low is not None and lows_stats:
                # Connect to Max_Low
                if max_low is not None:
                    self.plot(
                        [-1, -3], [apex_low_mean, max_low],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexLow-MaxLow Line'
                    )

                # Connect to MinAvg_L (highest 50% average low)
                if 'min_avg' in lows_stats:
                    self.plot(
                        [-1, -3], [apex_low_mean, lows_stats['min_avg']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexLow-MinAvgL Line'
                    )

                # Connect to Avg_L (average of all lows)
                if 'average' in lows_stats:
                    self.plot(
                        [-1, -3], [apex_low_mean, lows_stats['average']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexLow-AvgL Line'
                    )

                # Connect to MaxAvg_L (lowest 50% average low)
                if 'max_avg' in lows_stats:
                    self.plot(
                        [-1, -3], [apex_low_mean, lows_stats['max_avg']],
                        pen=pg.mkPen(color='white', width=2),  # Changed to 2pt
                        name='ApexLow-MaxAvgL Line'
                    )

                # Note: Not connecting to median as requested

        except Exception as e:
            print(f"Error adding apex_low connecting lines: {e}")

        # Add white vertical line at 0x from max low to max high with arrows at both ends
        try:
            if max_low is not None and max_high is not None:
                # Create the main vertical line
                self.plot(
                    [0, 0], [max_low, max_high],
                    pen=pg.mkPen(color='white', width=2),
                    name=f'Vertical Range: {max_low:.2f} to {max_high:.2f}'
                )

                # Add arrow at top (pointing up) - filled, dynamic sizing
                self.plot(
                    [0, -arrow_x_size, arrow_x_size, 0], [max_high, max_high - arrow_y_size, max_high - arrow_y_size, max_high],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Top Arrow'
                )

                # Add arrow at bottom (pointing down) - filled, dynamic sizing
                self.plot(
                    [0, -arrow_x_size, arrow_x_size, 0], [max_low, max_low + arrow_y_size, max_low + arrow_y_size, max_low],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Bottom Arrow'
                )

        except Exception as e:
            print(f"Error adding vertical line with arrows: {e}")

        # Set static axis limits using pre-calculated values
        try:
            if axis_limits:
                # Set static axis limits - anchored and fixed
                self.setXRange(axis_limits['x_min'], axis_limits['x_max'], padding=0)
                self.setYRange(axis_limits['y_min'], axis_limits['y_max'], padding=0)

                # Disable auto-ranging to keep it static and anchored
                self.getViewBox().setAutoVisible(x=False, y=False)
                self.getViewBox().enableAutoRange(enable=False)
                self.getViewBox().setMouseEnabled(x=False, y=False)  # Disable mouse interactions
                self.getViewBox().setMenuEnabled(False)  # Disable context menu

        except Exception as e:
            print(f"Error setting static axis limits: {e}")

        # Reposition title using pre-calculated position
        try:
            if hasattr(self, 'title_label') and title_position:
                self.title_label.setPos(title_position['x'], title_position['y'])

        except Exception as e:
            print(f"Error repositioning title: {e}")


class DensityChart(pg.PlotWidget):
    """Density Graph chart widget with projected highs/lows and connecting lines."""

    def __init__(self):
        super().__init__()
        self.setBackground('#1e1e1e')
        self.showGrid(x=False, y=False)  # Disable grid

        # Remove X and Y axes
        self.hideAxis('left')
        self.hideAxis('bottom')

        # Options display settings
        self.price_type = 'ask'  # Default to ask prices
        self.selected_expiry = None  # Will be set when data is loaded

        # Profit/loss line calculations
        self.global_average_iv = 0.0  # Global average IV for all profit/loss lines
        self._averages_ready = False  # Flag to indicate if averages have been computed
        self._first_load_complete = False  # Flag to track first successful data load
        self._initialization_attempts = 0  # Track initialization attempts
        self._max_init_attempts = 3  # Maximum attempts before giving up

        # Simple crosshair implementation
        self.crosshair_enabled = False
        self.mouse_proxy = None

    def enable_crosshair(self):
        """Enable crosshair functionality with direct mouse tracking."""
        if not self.crosshair_enabled:
            # Create crosshair lines with bright colors and high Z-value to be on top
            self.crosshair_v = pg.InfiniteLine(angle=90, movable=False,
                                             pen=pg.mkPen(color='red', width=5))
            self.crosshair_h = pg.InfiniteLine(angle=0, movable=False,
                                             pen=pg.mkPen(color='red', width=5))

            # Set high Z-value to ensure crosshair is on top
            self.crosshair_v.setZValue(1000)
            self.crosshair_h.setZValue(1000)

            # Position crosshair at center initially to make it visible
            self.crosshair_v.setPos(0)
            self.crosshair_h.setPos(600)  # Approximate center price

            # Add to plot with high priority
            self.addItem(self.crosshair_v, ignoreBounds=True)
            self.addItem(self.crosshair_h, ignoreBounds=True)

            # Create crosshair label with bright background and high Z-value
            self.crosshair_label = pg.TextItem(text="$600.00", color='yellow',
                                             fill=pg.mkBrush(0, 0, 0, 200),
                                             border=pg.mkPen(color='red', width=2))
            self.crosshair_label.setZValue(1001)
            self.crosshair_label.setPos(0.2, 602)  # Position near crosshair
            self.addItem(self.crosshair_label, ignoreBounds=True)

            # Connect to scene mouse move signal
            self.scene().sigMouseMoved.connect(self.on_mouse_moved)

            # Also enable mouse tracking on the widget
            self.setMouseTracking(True)

            # Enable mouse events on the plot item
            self.plotItem.vb.setMouseEnabled(x=True, y=True)

            self.crosshair_enabled = True
            # Crosshair enabled

    def on_mouse_moved(self, pos):
        """Handle mouse movement for crosshair display."""
        if not self.crosshair_enabled:
            return

        try:
            # Check if mouse is within the plot area
            if self.sceneBoundingRect().contains(pos):
                # Convert scene coordinates to view coordinates
                mouse_point = self.plotItem.vb.mapSceneToView(pos)
                x_pos = mouse_point.x()
                y_pos = mouse_point.y()

                # Update crosshair position
                self.crosshair_v.setPos(x_pos)
                self.crosshair_h.setPos(y_pos)

                # Update crosshair label with Y-axis value (price)
                self.crosshair_label.setText(f"${y_pos:.2f}")
                self.crosshair_label.setPos(x_pos + 0.2, y_pos + 2)  # Offset for visibility

            else:
                # Hide crosshair when mouse is outside
                self.crosshair_v.setPos(-10000)
                self.crosshair_h.setPos(-10000)
                self.crosshair_label.setText("")

        except Exception as e:
            print(f"Crosshair error: {e}")
            # Hide crosshair on any error
            if hasattr(self, 'crosshair_v') and self.crosshair_v:
                self.crosshair_v.setPos(-10000)
            if hasattr(self, 'crosshair_h') and self.crosshair_h:
                self.crosshair_h.setPos(-10000)
            if hasattr(self, 'crosshair_label') and self.crosshair_label:
                self.crosshair_label.setText("")

    def set_price_type(self, price_type):
        """Set the price type to display (bid or ask)."""
        self.price_type = price_type

    def set_selected_expiry(self, expiry):
        """Set the selected expiration date."""
        self.selected_expiry = expiry

    def calculate_arrow_sizes(self, projected_highs, projected_lows):
        """Calculate dynamic arrow sizes based on projected high/low range.

        Returns:
            tuple: (arrow_y_size, arrow_x_size) where:
                - arrow_y_size is 3% of the height range (highest high to lowest low)
                - arrow_x_size is 0.5% of the visual box range
        """
        try:
            if not projected_highs or not projected_lows:
                return 1.5, 0.05  # Fallback to fixed sizes

            # Calculate the full range from highest projected high to lowest projected low
            highest_high = max(projected_highs)
            lowest_low = min(projected_lows)
            total_range = highest_high - lowest_low

            # Arrow height: 3% of the total price range
            arrow_y_size = total_range * 0.03

            # Arrow width: 0.5% of visual box range (DensityChart: -5x to 5x = 10 units)
            arrow_x_size = 10 * 0.005  # 0.5% of 10 = 0.05

            return arrow_y_size, arrow_x_size

        except Exception as e:
            print(f"Error calculating arrow sizes: {e}")
            import traceback
            traceback.print_exc()
            return 1.5, 0.05  # Fallback to fixed sizes

    def create_continuous_spline(self, x_points, y_points, num_output_points=500, smoothness=0.5):
        """
        Create a continuous spline through multiple points with professional-grade rendering.
        Same method as used in density_graph.py for HL lines.

        Args:
            x_points: Array of x coordinates
            y_points: Array of y coordinates
            num_output_points: Number of points to generate for the output curve
            smoothness: Controls the smoothness of the curve (0.0 to 1.0)

        Returns:
            tuple: (x_out, y_out) arrays for the smooth curve
        """
        if len(x_points) < 2 or len(y_points) < 2:
            return np.array([]), np.array([])

        if len(x_points) != len(y_points):
            return np.array([]), np.array([])

        try:
            # Sort points by x value to ensure proper ordering
            sorted_indices = np.argsort(x_points)
            x_sorted = np.array(x_points)[sorted_indices]
            y_sorted = np.array(y_points)[sorted_indices]

            # Add intermediate points to simulate IV at micro steps between strikes
            # This creates a more continuous, natural flow like professional systems
            x_enhanced = []
            y_enhanced = []

            # For each pair of consecutive points, add micro-step points in between
            for i in range(len(x_sorted) - 1):
                # Add the current point
                x_enhanced.append(x_sorted[i])
                y_enhanced.append(y_sorted[i])

                # Calculate micro steps between this point and the next
                x_start = x_sorted[i]
                x_end = x_sorted[i + 1]
                y_start = y_sorted[i]
                y_end = y_sorted[i + 1]

                # Add micro steps (every 0.05 increment as suggested)
                x_step = 0.05
                x_current = x_start + x_step

                while x_current < x_end:
                    # Calculate y value using local cubic interpolation
                    # This simulates how IV would behave between strikes
                    t = (x_current - x_start) / (x_end - x_start)

                    # Use cubic Hermite interpolation for natural IV simulation
                    # h00, h10, h01, h11 are the Hermite basis functions
                    h00 = 2*t**3 - 3*t**2 + 1
                    h10 = t**3 - 2*t**2 + t
                    h01 = -2*t**3 + 3*t**2
                    h11 = t**3 - t**2

                    # Estimate tangents for natural curve shape
                    # Use finite differences for tangent estimation
                    m0 = 0
                    m1 = 0

                    # If we have points before and after, use them for better tangent estimation
                    if i > 0:
                        m0 = (y_end - y_sorted[i-1]) / (x_end - x_sorted[i-1]) * (x_end - x_start) * 0.5
                    else:
                        m0 = (y_end - y_start) / (x_end - x_start) * (x_end - x_start) * 0.5

                    if i < len(x_sorted) - 2:
                        m1 = (y_sorted[i+2] - y_start) / (x_sorted[i+2] - x_start) * (x_end - x_start) * 0.5
                    else:
                        m1 = (y_end - y_start) / (x_end - x_start) * (x_end - x_start) * 0.5

                    # Calculate interpolated y value
                    y_interp = h00 * y_start + h10 * m0 + h01 * y_end + h11 * m1

                    # Add the micro step point
                    x_enhanced.append(x_current)
                    y_enhanced.append(y_interp)

                    # Move to next micro step
                    x_current += x_step

            # Add the last point
            x_enhanced.append(x_sorted[-1])
            y_enhanced.append(y_sorted[-1])

            # Convert to numpy arrays
            x_enhanced = np.array(x_enhanced)
            y_enhanced = np.array(y_enhanced)

            # For B-spline, we need at least k+1 points for a degree k spline
            # With our enhanced points, we should always have enough points now
            if len(x_enhanced) >= 4:
                # Use B-spline for professional-grade smoothness
                # FIXED: Use s=0 to ensure exact interpolation through control points
                # This fixes the issue where curves don't pass through exact projected values

                # Create B-spline representation with cubic splines (k=3)
                # s=0 ensures the curve passes exactly through all control points
                tck = splrep(x_enhanced, y_enhanced, k=3, s=0)

                # Generate output points with sub-pixel resolution
                # Using more points creates smoother rendering with sub-pixel precision
                x_out = np.linspace(x_enhanced[0], x_enhanced[-1], num_output_points)
                y_out = splev(x_out, tck)

                # FIXED: Ensure exact passage through ALL control points
                # Force exact values at original control point x-coordinates
                if len(x_out) > 0 and len(y_out) > 0:
                    y_out[0] = y_sorted[0]   # Exact start point
                    y_out[-1] = y_sorted[-1] # Exact end point

                    # Also ensure exact passage through intermediate control points
                    for i, (orig_x, orig_y) in enumerate(zip(x_sorted, y_sorted)):
                        # Find the closest x position in the output array
                        closest_idx = np.argmin(np.abs(x_out - orig_x))
                        y_out[closest_idx] = orig_y  # Force exact y value

            else:
                # Use cubic spline for fewer points with exact interpolation
                cs = CubicSpline(x_enhanced, y_enhanced, bc_type='natural')
                x_out = np.linspace(x_enhanced[0], x_enhanced[-1], num_output_points)
                y_out = cs(x_out)

                # FIXED: Ensure exact passage through ALL control points for cubic spline too
                if len(x_out) > 0 and len(y_out) > 0:
                    y_out[0] = y_sorted[0]   # Exact start point
                    y_out[-1] = y_sorted[-1] # Exact end point

                    # Also ensure exact passage through intermediate control points
                    for i, (orig_x, orig_y) in enumerate(zip(x_sorted, y_sorted)):
                        # Find the closest x position in the output array
                        closest_idx = np.argmin(np.abs(x_out - orig_x))
                        y_out[closest_idx] = orig_y  # Force exact y value

            return x_out, y_out

        except Exception as e:
            print(f"Failed to create continuous spline: {str(e)}")
            return np.array([]), np.array([])

    def update_statistics_display(self, projected_highs, projected_lows, parent_tab=None):
        """Update the statistics display with volatility and projected statistics."""
        try:
            if not parent_tab:
                return

            # Clear existing combined statistics
            if hasattr(parent_tab, 'combined_statistics_layout'):
                # Clear all combined statistics
                while parent_tab.combined_statistics_layout.count():
                    child = parent_tab.combined_statistics_layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()

            # Add Bias Statistics section first (always shown regardless of mode)
            self.update_bias_statistics(parent_tab)

            if not projected_highs or not projected_lows:
                return

            # Add Volatility Statistics section title and headers to combined layout
            if hasattr(parent_tab, 'combined_statistics_layout'):
                # Add volatility statistics title
                volatility_stats_title = QLabel("Volatility Statistics")
                volatility_stats_title.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
                volatility_stats_title.setStyleSheet("color: #ffffff; margin-bottom: 5px; padding: 2px;")
                parent_tab.combined_statistics_layout.addWidget(volatility_stats_title)

                # Add volatility statistics headers
                volatility_headers_widget = QWidget()
                volatility_headers_layout = QHBoxLayout()
                volatility_headers_layout.setContentsMargins(0, 0, 0, 0)
                volatility_headers_layout.setSpacing(5)

                # Empty space for checkbox column
                checkbox_spacer = QLabel("")
                checkbox_spacer.setFixedWidth(20)
                volatility_headers_layout.addWidget(checkbox_spacer, 0)

                volatility_price_header = QLabel("Volatility Prices")
                volatility_price_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                volatility_price_header.setStyleSheet("color: #ffffff; text-align: left;")
                volatility_headers_layout.addWidget(volatility_price_header, 2)

                volatility_count_header = QLabel("Count")
                volatility_count_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                volatility_count_header.setStyleSheet("color: #ffffff; text-align: center;")
                volatility_count_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
                volatility_headers_layout.addWidget(volatility_count_header, 1)

                volatility_winrate_header = QLabel("Winrate")
                volatility_winrate_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                volatility_winrate_header.setStyleSheet("color: #ffffff; text-align: center;")
                volatility_winrate_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
                volatility_headers_layout.addWidget(volatility_winrate_header, 1)

                volatility_headers_widget.setLayout(volatility_headers_layout)
                parent_tab.combined_statistics_layout.addWidget(volatility_headers_widget)

            # Calculate and add volatility statistics data
            self.update_volatility_statistics(projected_highs, projected_lows, parent_tab)

            # Add projected statistics section title and headers to combined layout
            if hasattr(parent_tab, 'combined_statistics_layout'):
                # Add projected statistics title
                projected_stats_title = QLabel("Projected Statistics")
                projected_stats_title.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
                projected_stats_title.setStyleSheet("color: #ffffff; padding: 2px;")
                parent_tab.combined_statistics_layout.addWidget(projected_stats_title)

                # Add projected statistics headers
                projected_headers_widget = QWidget()
                projected_headers_layout = QHBoxLayout()
                projected_headers_layout.setContentsMargins(0, 0, 0, 0)
                projected_headers_layout.setSpacing(5)

                # Empty space for checkbox column
                checkbox_spacer2 = QLabel("")
                checkbox_spacer2.setFixedWidth(20)
                projected_headers_layout.addWidget(checkbox_spacer2, 0)

                price_header = QLabel("Projected Prices")
                price_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                price_header.setStyleSheet("color: #ffffff; text-align: left;")
                projected_headers_layout.addWidget(price_header, 2)

                count_header = QLabel("Count")
                count_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                count_header.setStyleSheet("color: #ffffff; text-align: center;")
                count_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
                projected_headers_layout.addWidget(count_header, 1)

                winrate_header = QLabel("Winrate")
                winrate_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
                winrate_header.setStyleSheet("color: #ffffff; text-align: center;")
                winrate_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
                projected_headers_layout.addWidget(winrate_header, 1)

                projected_headers_widget.setLayout(projected_headers_layout)
                parent_tab.combined_statistics_layout.addWidget(projected_headers_widget)

            # Then update projected statistics
            # Sort projected highs from high to low, then projected lows from high to low
            sorted_highs = sorted(projected_highs, reverse=True)  # Highest to lowest
            sorted_lows = sorted(projected_lows, reverse=True)   # Highest to lowest

            total_count = len(projected_highs) + len(projected_lows)

            # Add projected highs first (high to low order)
            for high_price in sorted_highs:
                # Create unified row widget with horizontal layout (1/2, 1/4, 1/4)
                row_widget = QWidget()
                row_layout = QHBoxLayout()
                row_layout.setContentsMargins(0, 0, 0, 0)
                row_layout.setSpacing(5)

                # Left section (1/2) - Projected High Price
                price_label = QLabel(f"Projected High Price $ at or below ${high_price:.2f}")
                price_label.setFont(QFont("Segoe UI", 7))
                price_label.setStyleSheet("color: #ffffff; padding: 1px;")

                # Middle section (1/4) - Count
                # For highs: count ALL values (both highs and lows) that are at or below this high price
                all_values_at_or_below = 0
                all_values_at_or_below += len([high for high in projected_highs if high <= high_price])
                all_values_at_or_below += len([low for low in projected_lows if low <= high_price])
                count = all_values_at_or_below

                count_label = QLabel(f"{count}/{total_count}")
                count_label.setFont(QFont("Segoe UI", 7))
                count_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Right section (1/4) - Winrate
                winrate = (count / total_count) * 100
                winrate_label = QLabel(f"{winrate:.1f}%")
                winrate_label.setFont(QFont("Segoe UI", 7))
                winrate_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                winrate_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Checkbox (same style as market_odds.py chart settings)
                checkbox = QCheckBox()
                checkbox.setChecked(False)  # Off by default
                self._style_statistics_checkbox(checkbox)

                # Connect checkbox to draw line functionality (now that count and winrate are calculated)
                checkbox.stateChanged.connect(
                    lambda state, price=high_price, is_high=True, c=count, wr=winrate:
                    self._on_projected_checkbox_changed(state, price, is_high, c, wr)
                )

                # Add widgets to layout
                row_layout.addWidget(checkbox, 0)  # No stretch, fixed size
                row_layout.addWidget(price_label, 2)  # 1/2 proportion
                row_layout.addWidget(count_label, 1)  # 1/4 proportion
                row_layout.addWidget(winrate_label, 1)  # 1/4 proportion

                row_widget.setLayout(row_layout)
                parent_tab.combined_statistics_layout.addWidget(row_widget)

            # Add projected lows second (high to low order)
            for low_price in sorted_lows:
                # Create unified row widget with horizontal layout (1/2, 1/4, 1/4)
                row_widget = QWidget()
                row_layout = QHBoxLayout()
                row_layout.setContentsMargins(0, 0, 0, 0)
                row_layout.setSpacing(5)

                # Left section (1/2) - Projected Low Price
                price_label = QLabel(f"Projected Low Price $ at or above ${low_price:.2f}")
                price_label.setFont(QFont("Segoe UI", 7))
                price_label.setStyleSheet("color: #ffffff; padding: 1px;")

                # Middle section (1/4) - Count
                # For lows: count ALL values (both highs and lows) that are at or above this low price
                all_values_at_or_above = 0
                all_values_at_or_above += len([high for high in projected_highs if high >= low_price])
                all_values_at_or_above += len([low for low in projected_lows if low >= low_price])
                count = all_values_at_or_above

                count_label = QLabel(f"{count}/{total_count}")
                count_label.setFont(QFont("Segoe UI", 7))
                count_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Right section (1/4) - Winrate
                winrate = (count / total_count) * 100
                winrate_label = QLabel(f"{winrate:.1f}%")
                winrate_label.setFont(QFont("Segoe UI", 7))
                winrate_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                winrate_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Checkbox (same style as market_odds.py chart settings)
                checkbox = QCheckBox()
                checkbox.setChecked(False)  # Off by default
                self._style_statistics_checkbox(checkbox)

                # Connect checkbox to draw line functionality (now that count and winrate are calculated)
                checkbox.stateChanged.connect(
                    lambda state, price=low_price, is_high=False, c=count, wr=winrate:
                    self._on_projected_checkbox_changed(state, price, is_high, c, wr)
                )

                # Add widgets to layout
                row_layout.addWidget(checkbox, 0)  # No stretch, fixed size
                row_layout.addWidget(price_label, 2)  # 1/2 proportion
                row_layout.addWidget(count_label, 1)  # 1/4 proportion
                row_layout.addWidget(winrate_label, 1)  # 1/4 proportion

                row_widget.setLayout(row_layout)
                parent_tab.combined_statistics_layout.addWidget(row_widget)

        except Exception as e:
            print(f"Error updating statistics display: {e}")

    def update_bias_statistics(self, parent_tab):
        """Update the bias statistics section based on weekday and H/L matching data."""
        try:
            # Get market data from parent tab
            market_data = getattr(parent_tab, '_market_data', None)
            if not market_data:
                return

            projected_ohlc_rows = market_data.get('projected_ohlc_table_rows', [])

            if not projected_ohlc_rows:
                return

            # Calculate weekday bias statistics
            weekday_bias = self._calculate_weekday_bias(projected_ohlc_rows)

            # Calculate H/L matching bias statistics
            hl_bias = self._calculate_hl_matching_bias(projected_ohlc_rows, market_data)

            # Add bias statistics section title and headers to combined layout
            if hasattr(parent_tab, 'combined_statistics_layout'):
                # Add bias statistics title
                bias_stats_title = QLabel("Bias")
                bias_stats_title.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
                bias_stats_title.setStyleSheet("color: #ffffff; margin-bottom: 5px; padding: 2px;")
                parent_tab.combined_statistics_layout.addWidget(bias_stats_title)

                # Add weekday bias statistics
                if weekday_bias:
                    for label, data in weekday_bias.items():
                        if isinstance(data, dict):
                            self._add_bias_row(parent_tab, label, data["percentage"], data["count"])
                        else:
                            self._add_bias_row(parent_tab, label, data, "")

                # Add H/L matching bias statistics
                if hl_bias:
                    for label, data in hl_bias.items():
                        if isinstance(data, dict):
                            self._add_bias_row(parent_tab, label, data["percentage"], data["count"])
                        else:
                            self._add_bias_row(parent_tab, label, data, "")

        except Exception as e:
            print(f"Error updating bias statistics: {e}")
            import traceback
            traceback.print_exc()

    def _calculate_weekday_bias(self, projected_ohlc_rows):
        """Calculate weekday-based bias statistics using today's weekday to predict next day performance."""
        try:
            # Get current weekday from the last row (most recent data)
            current_weekday = None
            if projected_ohlc_rows:
                last_row = projected_ohlc_rows[-1]
                if len(last_row) > 1:
                    current_weekday = last_row[1]  # Weekday is at index 1

            if not current_weekday:
                return {}

            # CORRECTED LOGIC: Use today's weekday data to predict tomorrow's performance
            # For each historical occurrence of today's weekday, look at what happened the NEXT day
            next_day_ratios = []

            for i in range(len(projected_ohlc_rows) - 1):  # Exclude last row since it has no "next day"
                current_row = projected_ohlc_rows[i]
                next_row = projected_ohlc_rows[i + 1]

                if len(current_row) > 1 and len(next_row) > 12:
                    current_day_weekday = current_row[1]  # Weekday of current day
                    next_day_close_ratio_str = next_row[12]  # Close ratio of NEXT day

                    # If current day matches today's weekday, use next day's performance
                    if current_day_weekday == current_weekday and next_day_close_ratio_str and next_day_close_ratio_str != "":
                        try:
                            next_day_close_ratio = float(next_day_close_ratio_str)
                            next_day_ratios.append(next_day_close_ratio)
                        except (ValueError, TypeError):
                            continue

            # Calculate bias percentages for next day based on historical next-day performance
            bias_results = {}
            if next_day_ratios:
                bullish_count = len([r for r in next_day_ratios if r > 1.0])
                bearish_count = len([r for r in next_day_ratios if r < 1.0])
                total_count = len(next_day_ratios)

                if total_count > 0:
                    bullish_pct = (bullish_count / total_count) * 100
                    bearish_pct = (bearish_count / total_count) * 100

                    # Show odds for NEXT DAY based on historical next-day performance after today's weekday
                    bias_results["Odds of Next Day Bullish"] = {"percentage": bullish_pct, "count": f"{bullish_count}/{total_count}"}
                    bias_results["Odds of Next Day Bearish"] = {"percentage": bearish_pct, "count": f"{bearish_count}/{total_count}"}

                    # Log corrected weekday bias calculation
                    print(f"Weekday Bias: Today is {current_weekday}, predicting tomorrow using {total_count} historical next-day occurrences - Bullish: {bullish_pct:.1f}% ({bullish_count}/{total_count}), Bearish: {bearish_pct:.1f}% ({bearish_count}/{total_count})")

            return bias_results

        except Exception as e:
            print(f"Error calculating weekday bias: {e}")
            return {}

    def _calculate_hl_matching_bias(self, projected_ohlc_rows, market_data):
        """Calculate H/L matching bias statistics."""
        try:
            # Get the last cycle information from crosshair data
            crosshair_info_lookup = market_data.get('crosshair_info_lookup', {})

            # Find the last cycle (most recent entry with category)
            last_cycle = None
            last_index = len(projected_ohlc_rows) - 1

            # Look backwards from the end to find the last cycle
            for i in range(last_index, -1, -1):
                if str(i) in crosshair_info_lookup:
                    info = crosshair_info_lookup[str(i)]
                    if isinstance(info, dict) and 'category' in info:
                        category = info['category']
                        if category and (category.startswith('H') or category.startswith('L')):
                            last_cycle = category.split('⇨')[0] if '⇨' in category else category
                            break

            if not last_cycle:
                return {}

            # Parse the last cycle to get type and number
            if last_cycle.startswith('H'):
                cycle_type = 'H'
                cycle_number = int(last_cycle[1:]) if len(last_cycle) > 1 else 1
            elif last_cycle.startswith('L'):
                cycle_type = 'L'
                cycle_number = int(last_cycle[1:]) if len(last_cycle) > 1 else 1
            else:
                return {}

            # Collect data for H/L matching analysis
            hl_data = {}

            for i, row in enumerate(projected_ohlc_rows):
                if len(row) > 12 and str(i) in crosshair_info_lookup:
                    info = crosshair_info_lookup[str(i)]
                    if isinstance(info, dict) and 'category' in info:
                        category = info['category']
                        if category and (category.startswith('H') or category.startswith('L')):
                            clean_category = category.split('⇨')[0] if '⇨' in category else category
                            close_ratio_str = row[12]  # Close ratio is at index 12

                            if close_ratio_str and close_ratio_str != "":
                                try:
                                    close_ratio = float(close_ratio_str)

                                    if clean_category not in hl_data:
                                        hl_data[clean_category] = []
                                    hl_data[clean_category].append(close_ratio)
                                except (ValueError, TypeError):
                                    continue

            # Calculate bias for what comes AFTER the current cycle
            # Use CURRENT CYCLE data to predict what happens next
            bias_results = {}

            # Use CURRENT cycle historical data (not target data!)
            if last_cycle in hl_data:
                current_cycle_ratios = hl_data[last_cycle]

                # Determine what to predict based on current cycle
                if cycle_type == 'H':
                    # If current cycle is H, predict odds of L1 and H(number+1)
                    target_l = 'L1'
                    target_h = f'H{cycle_number + 1}'
                elif cycle_type == 'L':
                    # If current cycle is L, predict odds of H1 and L(number+1)
                    target_h = 'H1'
                    target_l = f'L{cycle_number + 1}'
                else:
                    return {}

                # Calculate bias using CURRENT CYCLE data
                if current_cycle_ratios:
                    bullish_count = len([r for r in current_cycle_ratios if r > 1.0])
                    bearish_count = len([r for r in current_cycle_ratios if r < 1.0])
                    total_count = len(current_cycle_ratios)

                    if total_count > 0:
                        bullish_pct = (bullish_count / total_count) * 100
                        bearish_pct = (bearish_count / total_count) * 100

                        # Show predictions based on current cycle data
                        if cycle_type == 'H':
                            bias_results[f"Odds of Next Day {target_l}"] = {"percentage": bullish_pct, "count": f"{bullish_count}/{total_count}"}
                            bias_results[f"Odds of Next Day {target_h}"] = {"percentage": bearish_pct, "count": f"{bearish_count}/{total_count}"}
                            targets = f"{target_l} and {target_h}"
                        elif cycle_type == 'L':
                            bias_results[f"Odds of Next Day {target_h}"] = {"percentage": bullish_pct, "count": f"{bullish_count}/{total_count}"}
                            bias_results[f"Odds of Next Day {target_l}"] = {"percentage": bearish_pct, "count": f"{bearish_count}/{total_count}"}
                            targets = f"{target_h} and {target_l}"

                        # Log category bias calculation
                        print(f"Category Bias: Using {last_cycle} data ({total_count} occurrences) to predict {targets} - Bullish: {bullish_pct:.1f}% ({bullish_count}/{total_count}), Bearish: {bearish_pct:.1f}% ({bearish_count}/{total_count})")

            return bias_results

        except Exception as e:
            print(f"Error calculating H/L matching bias: {e}")
            return {}

    def _add_bias_row(self, parent_tab, label, percentage, count=""):
        """Add a bias statistics row to the combined layout."""
        try:
            # Create row widget
            row_widget = QWidget()
            row_layout = QHBoxLayout()
            row_layout.setContentsMargins(0, 0, 0, 0)
            row_layout.setSpacing(5)

            # Label (takes most of the space)
            label_widget = QLabel(label)
            label_widget.setFont(QFont("Segoe UI", 7))
            label_widget.setStyleSheet("color: #ffffff; padding: 1px;")
            row_layout.addWidget(label_widget, 2)  # 2/4 proportion

            # Count (if provided)
            if count:
                count_widget = QLabel(count)
                count_widget.setFont(QFont("Segoe UI", 7))
                count_widget.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                count_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
                row_layout.addWidget(count_widget, 1)  # 1/4 proportion

            # Percentage (takes remaining space)
            percentage_widget = QLabel(f"{percentage:.1f}%")
            percentage_widget.setFont(QFont("Segoe UI", 7))
            percentage_widget.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
            percentage_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            row_layout.addWidget(percentage_widget, 1)  # 1/4 proportion

            row_widget.setLayout(row_layout)
            parent_tab.combined_statistics_layout.addWidget(row_widget)

        except Exception as e:
            print(f"Error adding bias row: {e}")

    def update_bias_statistics_standalone(self):
        """Update bias statistics independently - called from main update_data method."""
        try:
            # Check if we have market data
            if not hasattr(self, '_market_data') or not self._market_data:
                return

            market_data = self._market_data
            projected_ohlc_rows = market_data.get('projected_ohlc_table_rows', [])

            if not projected_ohlc_rows:
                return

            # Calculate weekday bias statistics
            weekday_bias = self._calculate_weekday_bias(projected_ohlc_rows)

            # Calculate H/L matching bias statistics
            hl_bias = self._calculate_hl_matching_bias(projected_ohlc_rows, market_data)

            # Find the combined statistics layout
            if hasattr(self, 'combined_statistics_layout'):
                # Clear existing bias statistics (but not all statistics)
                self._clear_bias_statistics()

                # Add bias statistics title
                bias_stats_title = QLabel("Bias")
                bias_stats_title.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
                bias_stats_title.setStyleSheet("color: #ffffff; margin-bottom: 5px; padding: 2px;")
                bias_stats_title.setObjectName("bias_title")  # For identification
                self.combined_statistics_layout.insertWidget(0, bias_stats_title)  # Insert at top

                # Add bias column headers
                self._add_bias_headers()

                # Add weekday bias statistics
                if weekday_bias:
                    for label, data in weekday_bias.items():
                        if isinstance(data, dict):
                            self._add_bias_row_standalone(label, data["percentage"], data["count"])
                        else:
                            # Fallback for old format
                            self._add_bias_row_standalone(label, data, "")

                # Add H/L matching bias statistics
                if hl_bias:
                    for label, data in hl_bias.items():
                        if isinstance(data, dict):
                            self._add_bias_row_standalone(label, data["percentage"], data["count"])
                        else:
                            # Fallback for old format
                            self._add_bias_row_standalone(label, data, "")

        except Exception as e:
            print(f"Error updating standalone bias statistics: {e}")

    def _clear_bias_statistics(self):
        """Clear existing bias statistics from the layout."""
        try:
            if hasattr(self, 'combined_statistics_layout'):
                # Remove bias-related widgets
                for i in range(self.combined_statistics_layout.count() - 1, -1, -1):
                    item = self.combined_statistics_layout.itemAt(i)
                    if item and item.widget():
                        widget = item.widget()
                        if (hasattr(widget, 'objectName') and
                            (widget.objectName().startswith('bias_') or
                             widget.objectName() == 'bias_title' or
                             widget.objectName() == 'bias_headers')):
                            self.combined_statistics_layout.removeWidget(widget)
                            widget.deleteLater()
        except Exception as e:
            print(f"Error clearing bias statistics: {e}")

    def _add_bias_headers(self):
        """Add column headers for bias statistics."""
        try:
            # Create header widget
            header_widget = QWidget()
            header_widget.setObjectName("bias_headers")  # For identification
            header_layout = QHBoxLayout()
            header_layout.setContentsMargins(0, 0, 0, 0)
            header_layout.setSpacing(5)

            # Odds header (2/4 proportion)
            odds_header = QLabel("Odds")
            odds_header.setFont(QFont("Segoe UI", 7, QFont.Weight.Bold))
            odds_header.setStyleSheet("color: #ffffff; padding: 1px;")
            header_layout.addWidget(odds_header, 2)

            # Count header (1/4 proportion)
            count_header = QLabel("Count")
            count_header.setFont(QFont("Segoe UI", 7, QFont.Weight.Bold))
            count_header.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
            count_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
            header_layout.addWidget(count_header, 1)

            # Winrate header (1/4 proportion)
            winrate_header = QLabel("Winrate")
            winrate_header.setFont(QFont("Segoe UI", 7, QFont.Weight.Bold))
            winrate_header.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
            winrate_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
            header_layout.addWidget(winrate_header, 1)

            header_widget.setLayout(header_layout)

            # Insert after the bias title
            insert_position = 1  # Default position after title
            for i in range(self.combined_statistics_layout.count()):
                item = self.combined_statistics_layout.itemAt(i)
                if item and item.widget() and hasattr(item.widget(), 'objectName'):
                    if item.widget().objectName() == 'bias_title':
                        insert_position = i + 1
                        break

            self.combined_statistics_layout.insertWidget(insert_position, header_widget)

        except Exception as e:
            print(f"Error adding bias headers: {e}")

    def _add_bias_row_standalone(self, label, percentage, count=""):
        """Add a bias statistics row to the combined layout (standalone version)."""
        try:
            # Create row widget
            row_widget = QWidget()
            row_widget.setObjectName(f"bias_row_{len(label)}")  # For identification
            row_layout = QHBoxLayout()
            row_layout.setContentsMargins(0, 0, 0, 0)
            row_layout.setSpacing(5)

            # Label (takes most of the space)
            label_widget = QLabel(label)
            label_widget.setFont(QFont("Segoe UI", 7))
            label_widget.setStyleSheet("color: #ffffff; padding: 1px;")
            row_layout.addWidget(label_widget, 2)  # 2/4 proportion

            # Count (if provided)
            if count:
                count_widget = QLabel(count)
                count_widget.setFont(QFont("Segoe UI", 7))
                count_widget.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                count_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
                row_layout.addWidget(count_widget, 1)  # 1/4 proportion

            # Percentage (takes remaining space)
            percentage_widget = QLabel(f"{percentage:.1f}%")
            percentage_widget.setFont(QFont("Segoe UI", 7))
            percentage_widget.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
            percentage_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            row_layout.addWidget(percentage_widget, 1)  # 1/4 proportion

            row_widget.setLayout(row_layout)

            # Insert after the bias title (find its position)
            insert_position = 1  # Default position after title
            for i in range(self.combined_statistics_layout.count()):
                item = self.combined_statistics_layout.itemAt(i)
                if item and item.widget() and hasattr(item.widget(), 'objectName'):
                    if item.widget().objectName() == 'bias_title':
                        insert_position = i + 1
                        break

            # Find the last bias row position
            for i in range(insert_position, self.combined_statistics_layout.count()):
                item = self.combined_statistics_layout.itemAt(i)
                if item and item.widget() and hasattr(item.widget(), 'objectName'):
                    if not item.widget().objectName().startswith('bias_'):
                        insert_position = i
                        break
                else:
                    insert_position = i
                    break

            self.combined_statistics_layout.insertWidget(insert_position, row_widget)

        except Exception as e:
            print(f"Error adding standalone bias row: {e}")

    def update_volatility_statistics(self, projected_highs, projected_lows, parent_tab):
        """Update the volatility statistics section."""
        try:
            import statistics

            # Calculate statistics for highs and create list with counts
            highs_data = []
            if projected_highs:
                sorted_highs = sorted(projected_highs)
                n_highs = len(sorted_highs)

                # Calculate volatility levels for highs
                # Calculate apex as midpoint between max high and max low
                max_high_value = max(sorted_highs) if sorted_highs else None
                max_low_value = min(projected_lows) if projected_lows else None
                apex_value = None
                if max_high_value is not None and max_low_value is not None:
                    apex_value = (max_high_value + max_low_value) / 2.0

                highs_stats = {
                    'average': statistics.mean(sorted_highs),
                    'median': statistics.median(sorted_highs),
                    'max_avg': statistics.mean(sorted_highs[-max(1, n_highs // 2):]),  # Top 50%
                    'min_avg': statistics.mean(sorted_highs[:max(1, n_highs // 2)]),   # Bottom 50%
                    'apex': apex_value  # Midpoint between max high and max low
                }

                # Create list of highs data with counts for sorting
                for level_name, level_value in highs_stats.items():
                    count_at_or_below = len([high for high in projected_highs if high <= level_value])
                    winrate = (count_at_or_below / n_highs) * 100
                    highs_data.append((level_name, level_value, count_at_or_below, n_highs, winrate))

                # Sort highs by count (highest to lowest)
                highs_data.sort(key=lambda x: x[2], reverse=True)

                # Add volatility statistics for highs (ordered by count)
                for level_name, level_value, count_at_or_below, n_highs, winrate in highs_data:
                    # Create row for this volatility level
                    row_widget = QWidget()
                    row_layout = QHBoxLayout()
                    row_layout.setContentsMargins(0, 0, 0, 0)
                    row_layout.setSpacing(5)

                    # Checkbox (same style as market_odds.py chart settings)
                    checkbox = QCheckBox()
                    checkbox.setChecked(False)  # Off by default
                    self._style_statistics_checkbox(checkbox)

                    # Connect checkbox to draw line functionality
                    checkbox.stateChanged.connect(
                        lambda state, price=level_value, is_high=True, name=level_name, c=count_at_or_below, t=n_highs, wr=winrate:
                        self._on_volatility_checkbox_changed(state, price, is_high, name, c, t, wr)
                    )

                    row_layout.addWidget(checkbox, 0)  # No stretch, fixed size

                    # Description (1/2 width to match header)
                    desc_label = QLabel(f"High $ at or below {level_name.replace('_', ' ').title()}: ${level_value:.2f}")
                    desc_label.setFont(QFont("Segoe UI", 7))
                    desc_label.setStyleSheet("color: #ffffff; padding: 1px;")
                    row_layout.addWidget(desc_label, 2)  # 1/2 width

                    # Count (1/4 width to match header)
                    count_label = QLabel(f"{count_at_or_below}/{n_highs}")
                    count_label.setFont(QFont("Segoe UI", 7))
                    count_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                    count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    row_layout.addWidget(count_label, 1)  # 1/4 width

                    # Winrate (1/4 width to match header)
                    winrate_label = QLabel(f"{winrate:.1f}%")
                    winrate_label.setFont(QFont("Segoe UI", 7))
                    winrate_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                    winrate_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    row_layout.addWidget(winrate_label, 1)  # 1/4 width

                    row_widget.setLayout(row_layout)
                    parent_tab.combined_statistics_layout.addWidget(row_widget)

            # Calculate statistics for lows and create list with counts
            lows_data = []
            if projected_lows:
                sorted_lows = sorted(projected_lows)
                n_lows = len(sorted_lows)

                # Calculate volatility levels for lows
                # Calculate apex as midpoint between max high and max low (same as highs)
                max_high_value = max(projected_highs) if projected_highs else None
                max_low_value = min(sorted_lows) if sorted_lows else None
                apex_value = None
                if max_high_value is not None and max_low_value is not None:
                    apex_value = (max_high_value + max_low_value) / 2.0

                lows_stats = {
                    'average': statistics.mean(sorted_lows),
                    'median': statistics.median(sorted_lows),
                    'min_avg': statistics.mean(sorted_lows[-max(1, n_lows // 2):]),  # Top 50% (highest lows)
                    'max_avg': statistics.mean(sorted_lows[:max(1, n_lows // 2)]),   # Bottom 50% (lowest lows)
                    'apex': apex_value  # Midpoint between max high and max low
                }

                # Create list of lows data with counts for sorting
                for level_name, level_value in lows_stats.items():
                    count_at_or_above = len([low for low in projected_lows if low >= level_value])
                    winrate = (count_at_or_above / n_lows) * 100
                    lows_data.append((level_name, level_value, count_at_or_above, n_lows, winrate))

                # Sort lows by count (highest to lowest)
                lows_data.sort(key=lambda x: x[2], reverse=True)

                # Add volatility statistics for lows (ordered by count)
                for level_name, level_value, count_at_or_above, n_lows, winrate in lows_data:
                    # Create row for this volatility level
                    row_widget = QWidget()
                    row_layout = QHBoxLayout()
                    row_layout.setContentsMargins(0, 0, 0, 0)
                    row_layout.setSpacing(5)

                    # Checkbox (same style as market_odds.py chart settings)
                    checkbox = QCheckBox()
                    checkbox.setChecked(False)  # Off by default
                    self._style_statistics_checkbox(checkbox)

                    # Connect checkbox to draw line functionality
                    checkbox.stateChanged.connect(
                        lambda state, price=level_value, is_high=False, name=level_name, c=count_at_or_above, t=n_lows, wr=winrate:
                        self._on_volatility_checkbox_changed(state, price, is_high, name, c, t, wr)
                    )

                    row_layout.addWidget(checkbox, 0)  # No stretch, fixed size

                    # Description (1/2 width to match header)
                    desc_label = QLabel(f"Low $ at or above {level_name.replace('_', ' ').title()}: ${level_value:.2f}")
                    desc_label.setFont(QFont("Segoe UI", 7))
                    desc_label.setStyleSheet("color: #ffffff; padding: 1px;")
                    row_layout.addWidget(desc_label, 2)  # 1/2 width

                    # Count (1/4 width to match header)
                    count_label = QLabel(f"{count_at_or_above}/{n_lows}")
                    count_label.setFont(QFont("Segoe UI", 7))
                    count_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                    count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    row_layout.addWidget(count_label, 1)  # 1/4 width

                    # Winrate (1/4 width to match header)
                    winrate_label = QLabel(f"{winrate:.1f}%")
                    winrate_label.setFont(QFont("Segoe UI", 7))
                    winrate_label.setStyleSheet("color: #ffffff; padding: 1px; text-align: center;")
                    winrate_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    row_layout.addWidget(winrate_label, 1)  # 1/4 width

                    row_widget.setLayout(row_layout)
                    parent_tab.combined_statistics_layout.addWidget(row_widget)

        except Exception as e:
            print(f"Error updating volatility statistics: {e}")

    def _style_statistics_checkbox(self, checkbox):
        """Apply native PyQt6 styling to checkbox - smaller version for statistics."""
        # Set smaller size and remove borders
        checkbox.setStyleSheet("""
            QCheckBox {
                border: none;
                spacing: 2px;
            }
            QCheckBox::indicator {
                width: 12px;
                height: 12px;
                border: 1px solid #555555;
                border-radius: 2px;
                background-color: #2d2d2d;
            }
            QCheckBox::indicator:checked {
                background-color: #808080;
                border: 1px solid #606060;
            }
        """)

        # Set smaller font for compact appearance
        font = QFont("Consolas", 7)  # Even smaller font
        if not font.exactMatch():
            font = QFont("Courier New", 7)
        if not font.exactMatch():
            font = QFont("monospace", 7)
        checkbox.setFont(font)

        # Set smaller minimum height for compact appearance
        checkbox.setMinimumHeight(12)
        checkbox.setMaximumHeight(12)

        # Enable focus policy for keyboard navigation
        checkbox.setFocusPolicy(Qt.FocusPolicy.TabFocus)

    def _on_projected_checkbox_changed(self, state, price, is_high, count, winrate):
        """Handle projected statistics checkbox state changes."""
        try:
            # Find all chart widgets to draw lines on
            chart_widgets = self._find_chart_widgets()
            if not chart_widgets:
                return

            if state == Qt.CheckState.Checked.value:
                # Draw line when checked
                color = 'green' if is_high else 'red'
                percentage = winrate  # Use the winrate directly
                label = f"{percentage:.1f}%: ${price:.2f}"

                # Store references for removal later
                if not hasattr(self, '_projected_lines'):
                    self._projected_lines = {}

                lines_and_texts = []

                # Add infinite horizontal line to all chart widgets
                for chart_widget in chart_widgets:
                    # Add infinite horizontal line at the price level (no built-in label)
                    line = pg.InfiniteLine(
                        pos=price,
                        angle=0,  # Horizontal line
                        pen=pg.mkPen(color=color, width=2, style=pg.QtCore.Qt.PenStyle.DashLine)
                    )
                    chart_widget.addItem(line)

                    # Add text label - right-aligned
                    if is_high:
                        # Label above the line for highs
                        text_item = pg.TextItem(label, anchor=(1, 1), color=color)
                        # Get chart view range to position at right edge
                        view_range = chart_widget.viewRange()
                        x_max = view_range[0][1]  # Right edge of view
                        text_item.setPos(x_max, price)  # Position at right edge, above line
                    else:
                        # Label below the line for lows
                        text_item = pg.TextItem(label, anchor=(1, 0), color=color)
                        # Get chart view range to position at right edge
                        view_range = chart_widget.viewRange()
                        x_max = view_range[0][1]  # Right edge of view
                        text_item.setPos(x_max, price)  # Position at right edge, below line

                    chart_widget.addItem(text_item)
                    lines_and_texts.append((line, text_item, chart_widget))

                self._projected_lines[price] = lines_and_texts

            else:
                # Remove line when unchecked
                if hasattr(self, '_projected_lines') and price in self._projected_lines:
                    lines_and_texts = self._projected_lines[price]
                    for line, text_item, chart_widget in lines_and_texts:
                        chart_widget.removeItem(line)
                        chart_widget.removeItem(text_item)
                    del self._projected_lines[price]

        except Exception as e:
            print(f"Error handling projected checkbox change: {e}")

    def _on_volatility_checkbox_changed(self, state, price, is_high, name, count, total, winrate):
        """Handle volatility statistics checkbox state changes."""
        try:
            # Find all chart widgets to draw lines on
            chart_widgets = self._find_chart_widgets()
            if not chart_widgets:
                return

            if state == Qt.CheckState.Checked.value:
                # Draw line when checked
                color = 'green' if is_high else 'red'
                label = f"{winrate:.1f}%: ${price:.2f}"

                # Store references for removal later
                if not hasattr(self, '_volatility_lines'):
                    self._volatility_lines = {}

                lines_and_texts = []

                # Add infinite horizontal line to all chart widgets
                for chart_widget in chart_widgets:
                    # Add infinite horizontal line at the price level (no built-in label)
                    line = pg.InfiniteLine(
                        pos=price,
                        angle=0,  # Horizontal line
                        pen=pg.mkPen(color=color, width=2, style=pg.QtCore.Qt.PenStyle.DashLine)
                    )
                    chart_widget.addItem(line)

                    # Add text label - right-aligned
                    if is_high:
                        # Label above the line for highs
                        text_item = pg.TextItem(label, anchor=(1, 1), color=color)
                        # Get chart view range to position at right edge
                        view_range = chart_widget.viewRange()
                        x_max = view_range[0][1]  # Right edge of view
                        text_item.setPos(x_max, price)  # Position at right edge, above line
                    else:
                        # Label below the line for lows
                        text_item = pg.TextItem(label, anchor=(1, 0), color=color)
                        # Get chart view range to position at right edge
                        view_range = chart_widget.viewRange()
                        x_max = view_range[0][1]  # Right edge of view
                        text_item.setPos(x_max, price)  # Position at right edge, below line

                    chart_widget.addItem(text_item)
                    lines_and_texts.append((line, text_item, chart_widget))

                self._volatility_lines[f"{name}_{price}"] = lines_and_texts

            else:
                # Remove line when unchecked
                if hasattr(self, '_volatility_lines') and f"{name}_{price}" in self._volatility_lines:
                    lines_and_texts = self._volatility_lines[f"{name}_{price}"]
                    for line, text_item, chart_widget in lines_and_texts:
                        chart_widget.removeItem(line)
                        chart_widget.removeItem(text_item)
                    del self._volatility_lines[f"{name}_{price}"]

        except Exception as e:
            print(f"Error handling volatility checkbox change: {e}")

    def _find_chart_widgets(self):
        """Find all chart widgets to draw lines on (volatility, density, and FWL odds)."""
        try:
            # Navigate up the widget hierarchy to find the tab with chart widgets
            parent = self.parent()
            while parent:
                if hasattr(parent, 'volatility_chart') and hasattr(parent, 'density_chart') and hasattr(parent, 'fwl_odds_chart'):
                    return [parent.volatility_chart, parent.density_chart, parent.fwl_odds_chart]
                parent = parent.parent()
            return []
        except Exception as e:
            print(f"Error finding chart widgets: {e}")
            return []

    def update_data(self, filtered_high_data, filtered_low_data, market_data=None):
        """Update density chart with filtered data and market data."""
        # Clear existing plot items
        self.clear()

        # Enable crosshair after clearing
        self.enable_crosshair()

        # Store market data for use in profit/loss calculations
        self.current_market_data = market_data

        # Check if we have basic market data requirements
        if not market_data:
            return

        ticker = market_data.get("ticker", "")
        if not ticker:
            return

        # Get the data service from the parent tab to perform calculations
        parent_tab = self.parent()
        while parent_tab and not hasattr(parent_tab, 'data_service'):
            parent_tab = parent_tab.parent()

        if not parent_tab or not hasattr(parent_tab, 'data_service') or not parent_tab.data_service:
            print("No data service available for density calculations")
            return

        # Use backend service to calculate all data
        calc_data = parent_tab.data_service.calculate_density_data(
            filtered_high_data, filtered_low_data, market_data,
            self.price_type, getattr(self, 'selected_expiry', None)
        )

        if not calc_data:
            print("Failed to calculate density data")
            return

        # Extract calculated values
        title_info = calc_data.get('title_info', {})
        current_close = calc_data.get('current_close')
        projected_highs = calc_data.get('projected_highs', [])
        projected_lows = calc_data.get('projected_lows', [])
        lowest_low = calc_data.get('lowest_low')
        highest_high = calc_data.get('highest_high')
        highs_stats = calc_data.get('highs_stats', {})
        lows_stats = calc_data.get('lows_stats', {})
        options_data = calc_data.get('options_data')
        filtered_strikes = calc_data.get('filtered_strikes', [])
        options_averages = calc_data.get('options_averages', {})
        strike_options_data = calc_data.get('strike_options_data', {})
        complex_divisor = calc_data.get('complex_divisor', 13.25)
        axis_limits = calc_data.get('axis_limits', {})
        title_position = calc_data.get('title_position', {})

        # Store calculated values for use in other methods
        self.cached_divisor = complex_divisor
        self.cached_divisor_price_type = self.price_type

        # Log complex divisor calculation
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Complex divisor calculated: {self.cached_divisor:.6f} using {self.price_type} prices")

        # Add title to density chart using calculated values
        try:
            if title_info:
                title_text = title_info.get('title_text', '')
                title_label = pg.TextItem(title_text, color='white', anchor=(0, 0))  # Left-bottom anchor
                title_label.setFont(QFont("Segoe UI", 8))

                # Position at -5.25x, will be repositioned later
                title_label.setPos(-5.25, 0)  # Will be repositioned after Y-axis limits are calculated
                self.addItem(title_label)

                # Store title label reference to reposition it later after Y-axis limits are calculated
                self.title_label = title_label

        except Exception as e:
            print(f"Error adding title to density chart: {e}")

        # If no projected data is available, handle fallback case
        if not projected_highs and not projected_lows:
            # Create minimal projected data from market data if available
            if current_close is not None:
                # Create a minimal range around current price for options display
                price_range = current_close * 0.1  # 10% range
                fallback_lowest_low = current_close - price_range
                fallback_highest_high = current_close + price_range

                # Process options data with fallback range
                self.add_strike_prices(fallback_lowest_low, fallback_highest_high, market_data, current_close)
                return
            else:
                return

        # All projected data extraction is now done by the backend service
        # The calculated values are already extracted above

        # Add strike prices using calculated values
        try:
            if lowest_low is not None and highest_high is not None:
                # Add strike prices at -5x position per $1 increment with options data
                self.add_strike_prices(lowest_low, highest_high, market_data, current_close)
        except Exception as e:
            print(f"Error adding strike prices: {e}")

        # Add vertical lines at -1x and 0x using calculated values
        try:
            if lowest_low is not None and highest_high is not None:
                # Add vertical line at -1x
                self.plot(
                    [-1, -1], [lowest_low, highest_high],
                    pen=pg.mkPen(color='white', width=1),
                    name='Vertical Line -1x'
                )

                # Add vertical line at 0x
                self.plot(
                    [0, 0], [lowest_low, highest_high],
                    pen=pg.mkPen(color='white', width=1),
                    name='Vertical Line 0x'
                )
        except Exception as e:
            print(f"Error adding vertical lines: {e}")

        # Add connecting lines (smooth B-spline lines from -2x to 2.5x)
        try:
            if current_close is not None:
                # Create smooth B-spline lines from close price at -2x through projected low/high pairs to close price at 2.5x
                # Assuming same index correspondence between lows and highs
                min_length = min(len(projected_lows), len(projected_highs))
                for i in range(min_length):
                    # Control points: close at -2x through low at -1x, high at 0x, to close at 2.5x
                    x_points = [-2, -1, 0, 2.5]
                    y_points = [current_close, projected_lows[i], projected_highs[i], current_close]

                    # Create smooth B-spline curve using the same method as density_graph.py
                    x_smooth, y_smooth = self.create_continuous_spline(
                        x_points, y_points,
                        num_output_points=100,  # Smooth curve with 100 points
                        smoothness=0.5  # Medium smoothness
                    )

                    if len(x_smooth) > 0 and len(y_smooth) > 0:
                        # Plot the smooth curve
                        self.plot(
                            x_smooth, y_smooth,
                            pen=pg.mkPen(color='white', width=1),
                            name=f'Smooth HL Line {i}: {projected_lows[i]:.2f} to {projected_highs[i]:.2f}'
                        )
        except Exception as e:
            print(f"Error adding smooth connecting lines: {e}")

        # All statistical calculations are now done by the backend service
        # The calculated values are already extracted above

        # Add statistical lines for projected highs
        try:
            if highs_stats:
                # MaxAvg for projected high as light green dashed line from -5.5x to 5x
                if 'max_avg' in highs_stats:
                    self.plot(
                        [-5.5, 5], [highs_stats['max_avg'], highs_stats['max_avg']],
                        pen=pg.mkPen(color='lightgreen', width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
                        name=f'MaxAvg High: {highs_stats["max_avg"]:.2f}'
                    )

                    # Add label for MaxAvg High - left aligned above the line
                    max_avg_high_label = pg.TextItem(f'25%: ${highs_stats["max_avg"]:.2f}', color='white', anchor=(0, 1))
                    max_avg_high_label.setPos(-5.5, highs_stats['max_avg'])  # Left aligned, above the line
                    self.addItem(max_avg_high_label)

                # Median for projected high as dark green dashed line from -5.5x to 5x
                if 'median' in highs_stats:
                    self.plot(
                        [-5.5, 5], [highs_stats['median'], highs_stats['median']],
                        pen=pg.mkPen(color='darkgreen', width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
                        name=f'Median High: {highs_stats["median"]:.2f}'
                    )

                    # Add label for Median High - left aligned above the line
                    median_high_label = pg.TextItem(f'50%: ${highs_stats["median"]:.2f}', color='white', anchor=(0, 1))
                    median_high_label.setPos(-5.5, highs_stats['median'])  # Left aligned, above the line
                    self.addItem(median_high_label)
        except Exception as e:
            print(f"Error adding statistical lines for highs: {e}")

        # Add statistical lines for projected lows
        try:
            if lows_stats:
                # MaxAvg for projected low as light red dashed line from -5.5x to 5x
                if 'max_avg' in lows_stats:
                    self.plot(
                        [-5.5, 5], [lows_stats['max_avg'], lows_stats['max_avg']],
                        pen=pg.mkPen(color='lightcoral', width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
                        name=f'MaxAvg Low: {lows_stats["max_avg"]:.2f}'
                    )

                    # Add label for MaxAvg Low - left aligned below the line
                    max_avg_low_label = pg.TextItem(f'25%: ${lows_stats["max_avg"]:.2f}', color='white', anchor=(0, 0))
                    max_avg_low_label.setPos(-5.5, lows_stats['max_avg'])  # Left aligned, below the line
                    self.addItem(max_avg_low_label)

                # Median for projected low as dark red dashed line from -5.5x to 5x
                if 'median' in lows_stats:
                    self.plot(
                        [-5.5, 5], [lows_stats['median'], lows_stats['median']],
                        pen=pg.mkPen(color='darkred', width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
                        name=f'Median Low: {lows_stats["median"]:.2f}'
                    )

                    # Add label for Median Low - left aligned below the line
                    median_low_label = pg.TextItem(f'50%: ${lows_stats["median"]:.2f}', color='white', anchor=(0, 0))
                    median_low_label.setPos(-5.5, lows_stats['median'])  # Left aligned, below the line
                    self.addItem(median_low_label)
        except Exception as e:
            print(f"Error adding statistical lines for lows: {e}")

        # Add arrow lines at 1.75x using calculated values
        try:
            if current_close is not None and highest_high is not None and lowest_low is not None:
                # Calculate dynamic arrow sizes based on projected high/low range
                arrow_y_size, arrow_x_size = self.calculate_arrow_sizes(projected_highs, projected_lows)

                # Green line from last close to highest projected high at 1.75x with arrow head
                self.plot(
                    [1.75, 1.75], [current_close, highest_high],
                    pen=pg.mkPen(color='green', width=2),
                    name=f'Green Arrow: {current_close:.2f} to {highest_high:.2f}'
                )

                # Add arrow head pointing at highest projected high (pointing up)
                self.plot(
                    [1.75, 1.75 - arrow_x_size, 1.75 + arrow_x_size, 1.75],
                    [highest_high, highest_high - arrow_y_size, highest_high - arrow_y_size, highest_high],
                    pen=pg.mkPen(color='green', width=1),
                    brush=pg.mkBrush(color='green'),
                    fillLevel=0,
                    name='Green Arrow Head'
                )

                # Red line from last close to lowest projected low at 1.75x with arrow head
                self.plot(
                    [1.75, 1.75], [current_close, lowest_low],
                    pen=pg.mkPen(color='red', width=2),
                    name=f'Red Arrow: {current_close:.2f} to {lowest_low:.2f}'
                )

                # Add arrow head pointing at lowest projected low (pointing down)
                self.plot(
                    [1.75, 1.75 - arrow_x_size, 1.75 + arrow_x_size, 1.75],
                    [lowest_low, lowest_low + arrow_y_size, lowest_low + arrow_y_size, lowest_low],
                    pen=pg.mkPen(color='red', width=1),
                    brush=pg.mkBrush(color='red'),
                    fillLevel=0,
                    name='Red Arrow Head'
                )

        except Exception as e:
            print(f"Error adding arrow lines: {e}")

        # Set axis ranges using calculated values
        try:
            if axis_limits:
                self.setXRange(axis_limits['x_min'], axis_limits['x_max'], padding=0)
                self.setYRange(axis_limits['y_min'], axis_limits['y_max'], padding=0)

                # Disable auto-ranging to keep it static and anchored
                self.getViewBox().setAutoVisible(x=False, y=False)
                self.getViewBox().enableAutoRange(enable=False)
                self.getViewBox().setMouseEnabled(x=False, y=False)  # Disable mouse interactions
                self.getViewBox().setMenuEnabled(False)  # Disable context menu

                # Reposition title using calculated position
                if hasattr(self, 'title_label') and title_position:
                    self.title_label.setPos(title_position['x'], title_position['y'])

        except Exception as e:
            print(f"Error setting axis ranges: {e}")

        # Update statistics display
        try:
            if projected_highs and projected_lows:
                # Find parent tab to update statistics
                parent_widget = self.parent()
                while parent_widget and not hasattr(parent_widget, 'combined_statistics_layout'):
                    parent_widget = parent_widget.parent()

                if parent_widget:
                    self.update_statistics_display(projected_highs, projected_lows, parent_widget)
        except Exception as e:
            print(f"Error updating statistics display: {e}")

    def add_strike_prices(self, lowest_low, highest_high, market_data=None, current_close=None):
        """Add actual available strike prices from options data, only within the projected range."""
        try:
            # Add "Strikes" title Y using backend helper (1% above highest high)
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()
            title_y_position = highest_high + (highest_high - lowest_low) * 0.01
            if parent_tab and hasattr(parent_tab, 'data_service') and parent_tab.data_service:
                try:
                    title_y_position = parent_tab.data_service.density_calculations_service.calculate_strikes_title_y(
                        lowest_low, highest_high
                    )
                except Exception:
                    pass

            strikes_title = pg.TextItem(
                text="Strikes",
                color='white',
                anchor=(1, 0.5)  # Right-aligned
            )
            strikes_title.setPos(-4.5, title_y_position)
            self.addItem(strikes_title)

            # Add headers based on price type selection
            price_type_display = self.price_type.capitalize()

            # Call IV header at -3.5x (right-aligned)
            call_iv_title = pg.TextItem(
                text="Call IV",
                color='white',
                anchor=(1, 0.5)  # Right-aligned
            )
            call_iv_title.setPos(-3.5, title_y_position)
            self.addItem(call_iv_title)

            # Call price header at -4x (right-aligned)
            call_price_title = pg.TextItem(
                text=f"Call {price_type_display}",
                color='white',
                anchor=(1, 0.5)  # Right-aligned
            )
            call_price_title.setPos(-4, title_y_position)
            self.addItem(call_price_title)

            if self.price_type == 'ask':
                # Put ask header at 4.5x (left-aligned)
                put_price_title = pg.TextItem(
                    text="Put Ask",
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned
                )
                put_price_title.setPos(4.5, title_y_position)
                self.addItem(put_price_title)

                # Put IV header at 4x (left-aligned)
                put_iv_title = pg.TextItem(
                    text="Put IV",
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned
                )
                put_iv_title.setPos(4, title_y_position)
                self.addItem(put_iv_title)

            elif self.price_type == 'bid':
                # Put bid header at 4.5x (left-aligned)
                put_price_title = pg.TextItem(
                    text="Put Bid",
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned
                )
                put_price_title.setPos(4.5, title_y_position)
                self.addItem(put_price_title)

                # Put IV header at 4x (left-aligned)
                put_iv_title = pg.TextItem(
                    text="Put IV",
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned
                )
                put_iv_title.setPos(4, title_y_position)
                self.addItem(put_iv_title)

            # Get options data if market_data is available
            options_data = None
            if market_data:
                self._initialization_attempts += 1
                try:
                    # Try to get shared options data from parent tab first
                    parent_tab = self.parent()
                    while parent_tab and not hasattr(parent_tab, 'get_shared_options_data'):
                        parent_tab = parent_tab.parent()

                    if parent_tab and hasattr(parent_tab, 'get_shared_options_data'):
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.debug("Using shared options data from parent tab")
                        options_data = parent_tab.get_shared_options_data(market_data)
                    else:
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.debug("No parent tab found, fetching options data directly")
                        options_data = self.fetch_options_data(market_data)

                    if not options_data:
                        if self._initialization_attempts < self._max_init_attempts:
                            return  # Exit early if no options data, but allow retries
                        else:
                            return
                    else:
                        self._first_load_complete = True  # Mark successful first load
                except Exception:
                    if self._initialization_attempts < self._max_init_attempts:
                        return  # Exit early on error, but allow retries
                    else:
                        return
            else:
                return

            if options_data:
                # Use backend to filter strikes and return processed options data
                parent_tab = self.parent()
                while parent_tab and not hasattr(parent_tab, 'data_service'):
                    parent_tab = parent_tab.parent()
                filtered_strikes = []
                processed_options_data = options_data
                if parent_tab and hasattr(parent_tab, 'data_service') and parent_tab.data_service:
                    filtered_strikes, processed_options_data = (
                        parent_tab.data_service.density_calculations_service.get_filtered_strikes_and_options_data(
                            lowest_low, highest_high, options_data, getattr(self, 'selected_expiry', None)
                        )
                    )

                # First: compute and render averages (sets self.global_average_iv)
                self.add_options_averages(lowest_low, highest_high, processed_options_data, filtered_strikes)

                # Then: add strike labels, per-strike option data, and profit/loss lines
                for strike_price in filtered_strikes:
                    # Strike price at -4.5x (right-aligned)
                    strike_label = pg.TextItem(
                        text=f"{strike_price:.2f}",  # 2 decimals, no dollar sign
                        color='white',
                        anchor=(1, 0.5)  # Right-aligned
                    )
                    strike_label.setPos(-4.5, strike_price)
                    self.addItem(strike_label)

                    # Add options data for this strike
                    self.add_options_data_for_strike(strike_price, options_data)

                    # Add profit/loss lines for this strike (uses self.global_average_iv)
                    self.add_profit_loss_lines(strike_price, options_data)

                # Add current price line and label
                if current_close is not None:
                    self.add_current_price_line(current_close, lowest_low, highest_high)

        except Exception as e:
            print(f"Error adding strike prices: {e}")
            import traceback
            traceback.print_exc()

    def add_options_averages(self, lowest_low, highest_high, options_data, filtered_strikes):
        """Add average calculations for options data below the lowest projected low (visuals only)."""
        try:
            # Reset flags for a clean recalculation each update
            self.global_average_iv = 0.0
            self._averages_ready = False

            if not options_data or not filtered_strikes:
                return

            # Get backend-calculated averages and position
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()

            if not parent_tab or not hasattr(parent_tab, 'data_service') or not parent_tab.data_service:
                return

            calc = parent_tab.data_service.density_calculations_service.calculate_options_averages_for_strikes(
                lowest_low, highest_high, options_data, filtered_strikes, self.price_type
            )
            if not calc:
                return

            avg_y_position = calc.get('avg_y_position', lowest_low - (highest_high - lowest_low) * 0.01)

            # Add "Avg" label at -4.5x (right-aligned)
            avg_label = pg.TextItem(text="Avg", color='white', anchor=(1, 0.5))
            avg_label.setPos(-4.5, avg_y_position)
            self.addItem(avg_label)

            # Render call price average at -4x (right-aligned)
            call_price_avg = calc.get('call_price_avg')
            if call_price_avg is not None:
                call_price_label = pg.TextItem(text=f"{call_price_avg:.2f}", color='white', anchor=(1, 0.5))
                call_price_label.setPos(-4, avg_y_position)
                self.addItem(call_price_label)

            # Render call IV average at -3.5x (right-aligned)
            call_iv_avg = calc.get('call_iv_avg', 0.0)
            call_iv_label = pg.TextItem(text=f"{call_iv_avg:.3f}", color='white', anchor=(1, 0.5))
            call_iv_label.setPos(-3.5, avg_y_position)
            self.addItem(call_iv_label)

            # Render put IV average at 4x (left-aligned)
            put_iv_avg = calc.get('put_iv_avg', 0.0)
            put_iv_label = pg.TextItem(text=f"{put_iv_avg:.3f}", color='white', anchor=(0, 0.5))
            put_iv_label.setPos(4, avg_y_position)
            self.addItem(put_iv_label)

            # Store global average IV and readiness
            self.global_average_iv = calc.get('global_average_iv', 0.0)
            self._averages_ready = True
            self._first_load_complete = True

            # Render put price average at 4.5x (left-aligned)
            put_price_avg = calc.get('put_price_avg')
            if put_price_avg is not None:
                put_price_label = pg.TextItem(text=f"{put_price_avg:.2f}", color='white', anchor=(0, 0.5))
                put_price_label.setPos(4.5, avg_y_position)
                self.addItem(put_price_label)

        except Exception as e:
            print(f"Error adding options averages: {e}")
            import traceback
            traceback.print_exc()

    def add_current_price_line(self, current_close, lowest_low, highest_high):
        """Render current price line and label using backend-calculated label position."""
        try:
            # Add white dotted line from -5.5x to 5x at current price
            self.addItem(pg.PlotDataItem(
                x=[-5.5, 5],
                y=[current_close, current_close],
                pen=pg.mkPen(color='white', width=1, style=pg.QtCore.Qt.PenStyle.DotLine),
                connect='all'
            ))

            # Get label Y position from backend (2% above current by default)
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()
            label_y_position = current_close + (highest_high - lowest_low) * 0.02
            if parent_tab and hasattr(parent_tab, 'data_service') and parent_tab.data_service:
                try:
                    # Reuse calculate_strikes_title_y pattern but for current price offset logic if exists in future
                    # For now, local default remains; keeping hook for consistency
                    pass
                except Exception:
                    pass

            # Add label above the line (left-aligned)
            current_price_label = pg.TextItem(
                text=f"Current Price:\n${current_close:.2f}",
                color='white',
                anchor=(0, 0.5)  # Left-aligned
            )
            current_price_label.setPos(-5.5, label_y_position)
            self.addItem(current_price_label)

        except Exception as e:
            print(f"Error adding current price line: {e}")
            import traceback
            traceback.print_exc()

    def set_density_axis_ranges(self, projected_highs, projected_lows):
        """Set axis ranges for density chart anchored from -5x to 5x using backend calculations."""
        try:
            if not projected_highs or not projected_lows:
                return

            highest_high = max(projected_highs)
            lowest_low = min(projected_lows)

            # Get backend axis limits and title position
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()

            axis = {'x_min': -5.5, 'x_max': 5}
            title_pos = {'x': -5.25, 'y': highest_high + (highest_high - lowest_low) * 0.075}
            if parent_tab and hasattr(parent_tab, 'data_service') and parent_tab.data_service:
                try:
                    svc = parent_tab.data_service.density_calculations_service
                    axis = svc.calculate_axis_limits(highest_high, lowest_low)
                    title_pos = svc.calculate_title_position(highest_high, lowest_low)
                except Exception:
                    pass

            # Set static axis limits
            self.setXRange(axis['x_min'], axis['x_max'], padding=0)
            self.setYRange(axis['y_min'], axis['y_max'], padding=0)

            # Disable auto-ranging to keep it static and anchored
            self.getViewBox().setAutoVisible(x=False, y=False)
            self.getViewBox().enableAutoRange(enable=False)
            self.getViewBox().setMouseEnabled(x=False, y=False)
            self.getViewBox().setMenuEnabled(False)

            # Reposition title at backend-calculated position
            try:
                if hasattr(self, 'title_label') and self.title_label:
                    self.title_label.setPos(title_pos['x'], title_pos['y'])
            except Exception as e:
                print(f"Error repositioning title: {e}")

        except Exception as e:
            print(f"Error setting density axis ranges: {e}")

    def fetch_options_data(self, market_data, selected_expiry=None):
        """Fetch options data using the backend data service."""
        try:
            ticker = market_data.get("ticker", "")
            if not ticker:
                return None

            # Determine which expiry to use
            if selected_expiry is None:
                # Check if we have a stored selected expiry
                if self.selected_expiry:
                    selected_expiry = self.selected_expiry
                else:
                    # Let the backend service handle finding the default expiry
                    pass

            # Get the data service from the parent tab
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()

            if parent_tab and hasattr(parent_tab, 'data_service') and parent_tab.data_service:
                result = parent_tab.data_service.get_options_data(ticker, selected_expiry)
            else:
                print("No data service available for options data")
                return None

            # Store the selected expiry for future use
            if result and 'selected_expiry' in result:
                self.selected_expiry = result['selected_expiry']

            return result

        except Exception as e:
            print(f"Error fetching options data: {e}")
            import traceback
            traceback.print_exc()
            return None

    def add_options_data_for_strike(self, strike_price, options_data):
        """Render options data (ask/bid prices and IV) for a specific strike price using backend-calculated values."""
        try:
            if not options_data:
                return

            # Get backend-processed option values for this strike
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()

            if not parent_tab or not hasattr(parent_tab, 'data_service') or not parent_tab.data_service:
                return

            data = parent_tab.data_service.density_calculations_service.get_options_data_for_strike(
                strike_price, options_data, self.price_type, getattr(self, 'selected_expiry', None)
            )
            if not data:
                return

            call_price = data.get('call_price')
            call_iv = data.get('call_iv')
            put_price = data.get('put_price')
            put_iv = data.get('put_iv')

            # Display call price at -4x (right-aligned)
            if call_price is not None:
                call_price_label = pg.TextItem(text=f"{call_price:.2f}", color='white', anchor=(1, 0.5))
                call_price_label.setPos(-4, strike_price)
                self.addItem(call_price_label)

            # Display call IV at -3.5x (right-aligned)
            if call_iv is not None:
                call_iv_label = pg.TextItem(text=f"{call_iv:.3f}", color='white', anchor=(1, 0.5))
                call_iv_label.setPos(-3.5, strike_price)
                self.addItem(call_iv_label)

            # Display put price at 4.5x (left-aligned)
            if put_price is not None and self.price_type in ['ask', 'bid']:
                put_price_label = pg.TextItem(text=f"{put_price:.2f}", color='white', anchor=(0, 0.5))
                put_price_label.setPos(4.5, strike_price)
                self.addItem(put_price_label)

            # Display put IV at 4x (left-aligned)
            if put_iv is not None:
                put_iv_label = pg.TextItem(text=f"{put_iv:.3f}", color='white', anchor=(0, 0.5))
                put_iv_label.setPos(4, strike_price)
                self.addItem(put_iv_label)

            # Propagate logs for NaN->0 conversions to frontend logger
            for log_msg in data.get('logs', []):
                import logging
                logging.getLogger(__name__).info(log_msg)

        except Exception as e:
            print(f"Error adding options data for strike {strike_price}: {e}")
            import traceback
            traceback.print_exc()

    def add_profit_loss_lines(self, strike_price, options_data):
        """Render call/put P&L curves using backend-computed points and backend divisor updates."""
        try:
            if not options_data:
                return

            # Acquire data service
            parent_tab = self.parent()
            while parent_tab and not hasattr(parent_tab, 'data_service'):
                parent_tab = parent_tab.parent()
            if not parent_tab or not hasattr(parent_tab, 'data_service') or not parent_tab.data_service:
                return

            market_data = getattr(self, 'current_market_data', {})

            # Compute curve points and divisor update in backend
            res = parent_tab.data_service.density_calculations_service.compute_profit_loss_curves(
                strike_price=strike_price,
                options_data=options_data,
                price_type=self.price_type,
                selected_expiry=getattr(self, 'selected_expiry', None),
                market_data=market_data,
                cached_divisor=getattr(self, 'cached_divisor', None),
                cached_divisor_price_type=getattr(self, 'cached_divisor_price_type', None),
            )
            if not res:
                return

            # Update cached divisor if backend indicates change
            if res.get('divisor_updated'):
                self.cached_divisor = res.get('divisor', self.cached_divisor)
                self.cached_divisor_price_type = self.price_type

            curves = res.get('curves', {})

            # Helper to plot a curve
            def _plot_curve(curve, color):
                if not curve:
                    return
                smooth = curve.get('smooth')
                base = curve.get('base')
                if smooth:
                    self.addItem(pg.PlotDataItem(x=smooth['x'], y=smooth['y'], pen=pg.mkPen(color=color, width=1), connect='all'))
                elif base:
                    self.addItem(pg.PlotDataItem(x=base['x'], y=base['y'], pen=pg.mkPen(color=color, width=1), connect='all'))

            # Render call (white) and put (white) curves as before
            _plot_curve(curves.get('call'), 'white')
            _plot_curve(curves.get('put'), 'white')

        except Exception as e:
            print(f"Error adding profit/loss lines for strike {strike_price}: {e}")
            import traceback
            traceback.print_exc()
        except Exception as e:
            print(f"Error adding profit/loss lines for strike {strike_price}: {e}")
            import traceback
            traceback.print_exc()

class FWLOddsChart(pg.PlotWidget):
    """FWL Odds chart widget with rectangle and price labels."""

    def __init__(self):
        super().__init__()
        self.setBackground('#1e1e1e')
        self.showGrid(x=False, y=False)  # Disable grid

        # Remove X and Y axes like volatility chart
        self.hideAxis('left')
        self.hideAxis('bottom')

        # Store chart items for cleanup
        self.chart_items = []
        self.text_items = []

        # Simple crosshair implementation
        self.crosshair_enabled = False
        self.mouse_proxy = None

    def enable_crosshair(self):
        """Enable crosshair functionality with direct mouse tracking."""
        if not self.crosshair_enabled:
            # Create crosshair lines with bright colors and high Z-value to be on top
            self.crosshair_v = pg.InfiniteLine(angle=90, movable=False,
                                             pen=pg.mkPen(color='red', width=5))
            self.crosshair_h = pg.InfiniteLine(angle=0, movable=False,
                                             pen=pg.mkPen(color='red', width=5))

            # Set high Z-value to ensure crosshair is on top
            self.crosshair_v.setZValue(1000)
            self.crosshair_h.setZValue(1000)

            # Position crosshair at center initially to make it visible
            self.crosshair_v.setPos(1)  # Center X position
            self.crosshair_h.setPos(600)  # Approximate center price

            # Add to plot with high priority
            self.addItem(self.crosshair_v, ignoreBounds=True)
            self.addItem(self.crosshair_h, ignoreBounds=True)

            # Create crosshair label with bright background and high Z-value
            self.crosshair_label = pg.TextItem(text="$600.00", color='yellow',
                                             fill=pg.mkBrush(0, 0, 0, 200),
                                             border=pg.mkPen(color='red', width=2))
            self.crosshair_label.setZValue(1001)
            self.crosshair_label.setPos(1.2, 602)  # Position near crosshair
            self.addItem(self.crosshair_label, ignoreBounds=True)

            # Connect to scene mouse move signal
            self.scene().sigMouseMoved.connect(self.on_mouse_moved)

            # Also enable mouse tracking on the widget
            self.setMouseTracking(True)

            # Enable mouse events on the plot item
            self.plotItem.vb.setMouseEnabled(x=True, y=True)

            self.crosshair_enabled = True
            # Crosshair enabled

    def on_mouse_moved(self, pos):
        """Handle mouse movement for crosshair display."""
        if not self.crosshair_enabled:
            return

        try:
            # Check if mouse is within the plot area
            if self.sceneBoundingRect().contains(pos):
                # Convert scene coordinates to view coordinates
                mouse_point = self.plotItem.vb.mapSceneToView(pos)
                x_pos = mouse_point.x()
                y_pos = mouse_point.y()

                # Update crosshair position
                self.crosshair_v.setPos(x_pos)
                self.crosshair_h.setPos(y_pos)

                # Update crosshair label with Y-axis value (price)
                self.crosshair_label.setText(f"${y_pos:.2f}")
                self.crosshair_label.setPos(x_pos + 0.2, y_pos + 2)  # Offset for visibility

            else:
                # Hide crosshair when mouse is outside
                self.crosshair_v.setPos(-10000)
                self.crosshair_h.setPos(-10000)
                self.crosshair_label.setText("")

        except Exception as e:
            print(f"Crosshair error: {e}")
            # Hide crosshair on any error
            if hasattr(self, 'crosshair_v') and self.crosshair_v:
                self.crosshair_v.setPos(-10000)
            if hasattr(self, 'crosshair_h') and self.crosshair_h:
                self.crosshair_h.setPos(-10000)
            if hasattr(self, 'crosshair_label') and self.crosshair_label:
                self.crosshair_label.setText("")

    def calculate_arrow_sizes(self, projected_highs, projected_lows):
        """Calculate dynamic arrow sizes based on projected high/low range.

        Returns:
            tuple: (arrow_y_size, arrow_x_size) where:
                - arrow_y_size is 3% of the height range (highest high to lowest low)
                - arrow_x_size is 0.5% of the visual box range
        """
        try:
            if not projected_highs or not projected_lows:
                return 1.5, 0.05  # Fallback to fixed sizes

            # Calculate the full range from highest projected high to lowest projected low
            highest_high = max(projected_highs)
            lowest_low = min(projected_lows)
            total_range = highest_high - lowest_low

            # Arrow height: 3% of the total price range
            arrow_y_size = total_range * 0.03

            # Arrow width: 0.5% of visual box range (FWLOddsChart: -0.5x to 7.5x = 8 units)
            arrow_x_size = 8 * 0.005 # 0.5% of 8 = 0.08

            return arrow_y_size, arrow_x_size

        except Exception as e:
            print(f"Error calculating arrow sizes: {e}")
            import traceback
            traceback.print_exc()
            return 1.5, 0.05  # Fallback to fixed sizes

    def update_data(self, filtered_high_data, filtered_low_data, market_data=None):
        """Update FWL Odds chart with rectangle, price labels, odds, and last close line."""
        try:
            # Clear existing items (EXACT same as volatility chart)
            self.clear()

            # Enable crosshair after clearing
            self.enable_crosshair()

            if not market_data:
                return

            # Add title to top left (EXACT same as volatility chart)
            try:
                ticker = market_data.get("ticker", "Unknown")
                timeframe = market_data.get("timeframe", "Unknown")
                dtl = market_data.get("dtl", 0)
                length = market_data.get("length", 0)

                # Calculate occurrences using the formula: (occurrences / 2) - 1
                total_occurrences = 0
                if filtered_high_data:
                    total_occurrences += len(filtered_high_data)
                if filtered_low_data:
                    total_occurrences += len(filtered_low_data)

                occurrences = int((total_occurrences / 2) - 1) if total_occurrences > 0 else 0

                # Create multi-line title format
                title_text = f'{ticker} Length: [{length}] {timeframe} Aggregations: {dtl}\noccurrence: {occurrences}'
                title_label = pg.TextItem(title_text, color='white', anchor=(0, 0))  # Left-bottom anchor
                title_label.setFont(QFont("Segoe UI", 8))

                # Position at -0.25x, will be repositioned to max_high after Y-axis limits are set
                title_label.setPos(-0.25, 0)  # Will be repositioned after Y-axis limits are set
                self.addItem(title_label)

                # Store title label reference to reposition it later after Y-axis limits are calculated
                self.title_label = title_label

            except Exception as e:
                print(f"Error adding title to FWL Odds chart: {e}")

            # Extract projected highs and lows
            projected_highs = []
            projected_lows = []

            # Extract projected highs from filtered high data
            if filtered_high_data:
                for row in filtered_high_data:
                    if len(row) > 6:  # Projected High is at index 6
                        try:
                            projected_high_str = str(row[6])
                            if projected_high_str and projected_high_str != '' and projected_high_str != 'None':
                                projected_high = float(projected_high_str)
                                projected_highs.append(projected_high)
                        except (ValueError, TypeError):
                            continue

            # Extract projected lows from filtered low data
            if filtered_low_data:
                for row in filtered_low_data:
                    if len(row) > 6:  # Projected Low is at index 6
                        try:
                            projected_low_str = str(row[6])
                            if projected_low_str and projected_low_str != '' and projected_low_str != 'None':
                                projected_low = float(projected_low_str)
                                projected_lows.append(projected_low)
                        except (ValueError, TypeError):
                            continue

            # Get last close price from market data
            last_close = None
            if market_data and 'close' in market_data:
                try:
                    close_data = market_data['close']
                    if close_data is not None and len(close_data) > 0:
                        last_close = float(close_data[-1])
                except (ValueError, TypeError, IndexError):
                    pass

            # Only proceed if we have both highs and lows
            if not projected_highs or not projected_lows:
                return

            # Draw enhanced rectangle with flow lines and standard deviation markers
            if last_close is not None:
                self.draw_rectangle(projected_highs, projected_lows, last_close, market_data)

            # Add projected high labels at 3.5x
            self.add_price_labels(projected_highs, 3.5, "High")

            # Add projected low labels at 5.5x
            self.add_price_labels(projected_lows, 5.5, "Low")

            # Calculate and add odds for projected highs at 4.5x
            self.add_odds_labels(projected_highs, 4.5, "High")

            # Calculate and add odds for projected lows at 6.5x
            self.add_odds_labels(projected_lows, 6.5, "Low")

            # Add white dotted line for last close price
            if last_close is not None:
                self.draw_last_close_line(last_close)

            # Add arrow lines at 5x and 7x positions
            if last_close is not None and projected_highs and projected_lows:
                self.draw_arrow_lines(projected_highs, projected_lows, last_close)

            # Set appropriate axis ranges (extend to 8.0 to accommodate all elements including 7x lines and labels)
            self.set_axis_ranges(projected_highs, projected_lows, extend_x=8.0)

        except Exception as e:
            print(f"Error updating FWL Odds chart: {e}")

    def clear_chart(self):
        """Clear all chart items."""
        for item in self.chart_items:
            self.removeItem(item)
        for item in self.text_items:
            self.removeItem(item)
        self.chart_items.clear()
        self.text_items.clear()

    def draw_rectangle(self, projected_highs, projected_lows, current_price, market_data=None):
        """Draw enhanced rectangle with green/red flow lines and standard deviation markers."""
        try:
            # Calculate rectangle bounds
            lowest_low = min(projected_lows)
            highest_high = max(projected_highs)

            # Rectangle coordinates: x from 0x to 3x (representing 1% to 50%), y from lowest low to highest high
            x1, x2 = 0.0, 3.0  # 0x to 3x on chart (representing 1% to 50% probability)
            y1, y2 = lowest_low, highest_high

            # Draw rectangle outline
            rect_x = [x1, x2, x2, x1, x1]  # Close the rectangle
            rect_y = [y1, y1, y2, y2, y1]  # Close the rectangle

            rect_item = self.plot(
                rect_x, rect_y,
                pen=pg.mkPen(color='white', width=2),
                name='FWL Rectangle'
            )
            self.chart_items.append(rect_item)

            # Draw flow lines for highs and lows
            self.draw_flow_lines(projected_highs, projected_lows, x1, x2)

            # Draw features in correct layering order (bottom to top)
            # Layer 1 (bottom): Light grey dotted lines
            self.draw_dotted_lines_for_projections(projected_highs, projected_lows)

            # Layer 2: Green and red flow lines
            self.draw_flow_lines(projected_highs, projected_lows, x1, x2)

            # Layer 3: Intersection marker and price labels
            self.draw_intersection_dot(projected_highs, projected_lows)
            self.draw_price_labels_at_05x(current_price, highest_high, lowest_low)
            self.draw_labels_at_25x(projected_highs, projected_lows)

            # Layer 4: White lines (vertical, diagonal, horizontal)
            self.draw_white_lines_at_225x(projected_highs, projected_lows)
            self.draw_lines_from_close_at_25x(current_price, projected_highs, projected_lows)
            self.draw_diagonal_lines_from_25x(projected_highs, projected_lows)

            # Layer 5: White rectangle
            self.draw_rectangle_outline(x1, x2, lowest_low, highest_high)

            # Layer 6 (top): Last close line will be drawn separately

            # Set proper axis ranges
            self.set_fwl_axis_ranges(projected_highs, projected_lows)

        except Exception as e:
            print(f"Error drawing rectangle: {e}")



    def set_fwl_axis_ranges(self, projected_highs, projected_lows):
        """Set axis ranges like volatility chart with proper anchoring."""
        try:
            if not projected_highs or not projected_lows:
                return

            # Calculate range like volatility chart
            all_prices = projected_highs + projected_lows
            min_price = min(all_prices)
            max_price = max(all_prices)
            price_range = max_price - min_price
            padding = price_range * 0.10  # 10% padding like volatility chart

            y_min = min_price - padding
            y_max = max_price + padding

            # Set static axis limits - anchored exactly at -0.5x to 7.5x
            self.setXRange(-0.5, 7.5, padding=0)  # X-axis exactly from -0.5x to 7.5x
            self.setYRange(y_min, y_max, padding=0)  # Y-axis with 10% padding

            # Force exact axis limits without any auto-adjustment
            self.getViewBox().setLimits(xMin=-0.5, xMax=7.5, yMin=y_min, yMax=y_max)

            # Disable auto-ranging to keep it static and anchored like volatility chart
            self.getViewBox().setAutoVisible(x=False, y=False)
            self.getViewBox().enableAutoRange(enable=False)
            self.getViewBox().setMouseEnabled(x=False, y=False)  # Disable mouse interactions
            self.getViewBox().setMenuEnabled(False)  # Disable context menu

            # Reposition title at -0.25x and 7.5% of range above max_high after Y-axis limits are set
            try:
                if hasattr(self, 'title_label') and max_price is not None and min_price is not None:
                    # Calculate 7.5% of the range (max_price - min_price) above max_price
                    range_offset = price_range * 0.075  # 7.5% of the range
                    title_y = max_price + range_offset
                    self.title_label.setPos(-0.25, title_y)

            except Exception as e:
                print(f"Error repositioning title: {e}")

            # Set title_y for section titles
            title_y = max_price + (price_range * 0.075) if max_price is not None and min_price is not None else max_price

            # Add additional titles at same height as main title
            self.add_section_titles(title_y, projected_highs, projected_lows)

        except Exception as e:
            print(f"Error setting FWL axis ranges: {e}")

    def add_section_titles(self, title_y, projected_highs, projected_lows):
        """Add section titles at same height as main title."""
        try:
            # Bullish Moves Price - at exactly 3.5x (left aligned)
            bullish_price_x = 3.5
            bullish_price_label = pg.TextItem('Bullish Moves\nPrice', color='white', anchor=(0, 0))  # Left aligned
            bullish_price_label.setFont(QFont("Segoe UI", 8))
            bullish_price_label.setPos(bullish_price_x, title_y)
            self.addItem(bullish_price_label)
            self.text_items.append(bullish_price_label)

            # Odds for bullish moves - at exactly 4.5x (left aligned)
            bullish_odds_x = 4.5
            bullish_odds_label = pg.TextItem('Odds', color='white', anchor=(0, 0))  # Left aligned
            bullish_odds_label.setFont(QFont("Segoe UI", 8))
            bullish_odds_label.setPos(bullish_odds_x, title_y)
            self.addItem(bullish_odds_label)
            self.text_items.append(bullish_odds_label)

            # Bearish Moves Price - at exactly 5.5x (left aligned, same height as bullish)
            bearish_price_x = 5.5
            bearish_price_label = pg.TextItem('Bearish Moves\nPrice', color='white', anchor=(0, 0))  # Left aligned
            bearish_price_label.setFont(QFont("Segoe UI", 8))
            bearish_price_label.setPos(bearish_price_x, title_y)  # Same height as bullish
            self.addItem(bearish_price_label)
            self.text_items.append(bearish_price_label)

            # Odds for bearish moves - at exactly 6.5x (left aligned, same height as bullish)
            bearish_odds_x = 6.5
            bearish_odds_label = pg.TextItem('Odds', color='white', anchor=(0, 0))  # Left aligned
            bearish_odds_label.setFont(QFont("Segoe UI", 8))
            bearish_odds_label.setPos(bearish_odds_x, title_y)  # Same height as bullish
            self.addItem(bearish_odds_label)
            self.text_items.append(bearish_odds_label)

        except Exception as e:
            print(f"Error adding section titles: {e}")

    def draw_rectangle_outline(self, x1, x2, lowest_low, highest_high):
        """Draw white rectangle outline."""
        try:
            # Draw rectangle outline
            rect_x = [x1, x2, x2, x1, x1]  # Rectangle coordinates
            rect_y = [lowest_low, lowest_low, highest_high, highest_high, lowest_low]

            rectangle_outline = self.plot(
                rect_x, rect_y,
                pen=pg.mkPen(color='white', width=2),
                name='Rectangle Outline'
            )
            self.chart_items.append(rectangle_outline)

        except Exception as e:
            print(f"Error drawing rectangle outline: {e}")

    def draw_flow_lines(self, projected_highs, projected_lows, x1, x2):
        """Draw green and red B-spline flow lines (0x=1%, 2.25x=50%)."""
        try:
            # Sort highs and lows to create flow lines
            sorted_highs = sorted(projected_highs, reverse=True)  # Highest to lowest
            sorted_lows = sorted(projected_lows)  # Lowest to highest

            # Create x-coordinates for flow (0x to 2.25x representing 1% to 50%)
            x_50_percent = 2.25  # 50% is now at 2.25x
            n_highs = len(sorted_highs)
            n_lows = len(sorted_lows)

            # Green B-spline for highs flow (highest prices on left at 0x, flowing to lower prices on right at 2.25x)
            if n_highs > 2:  # Need at least 3 points for B-spline
                x_highs = [x1 + (x_50_percent - x1) * i / (n_highs - 1) for i in range(n_highs)]

                # Create B-spline interpolation with rectangle constraints
                try:
                    from scipy.interpolate import make_interp_spline
                    import numpy as np

                    # Calculate rectangle bounds for constraints
                    all_prices = sorted_highs + sorted_lows
                    min_rect_price = min(all_prices)
                    max_rect_price = max(all_prices)

                    # Create smooth B-spline curve
                    x_smooth = np.linspace(x_highs[0], x_highs[-1], 100)  # 100 points for smooth curve
                    spline = make_interp_spline(x_highs, sorted_highs, k=min(3, n_highs-1))  # Cubic or lower order
                    y_smooth = spline(x_smooth)

                    # Constrain B-spline to stay within rectangle bounds
                    y_smooth = np.clip(y_smooth, min_rect_price, max_rect_price)

                    green_line = self.plot(
                        x_smooth, y_smooth,
                        pen=pg.mkPen(color='green', width=3),
                        name='Highs Flow B-spline (1%-50%)'
                    )
                    self.chart_items.append(green_line)
                except ImportError:
                    # Fallback to linear interpolation if scipy not available
                    green_line = self.plot(
                        x_highs, sorted_highs,
                        pen=pg.mkPen(color='green', width=3),
                        name='Highs Flow (1%-50%)'
                    )
                    self.chart_items.append(green_line)
            elif n_highs == 2:
                # Linear line for 2 points
                x_highs = [x1, x_50_percent]
                green_line = self.plot(
                    x_highs, sorted_highs,
                    pen=pg.mkPen(color='green', width=3),
                    name='Highs Flow (1%-50%)'
                )
                self.chart_items.append(green_line)
            elif n_highs == 1:
                # Single point at middle (1.125x representing ~25%)
                x_mid = (x1 + x_50_percent) / 2
                green_point = self.plot(
                    [x_mid], sorted_highs,
                    pen=pg.mkPen(color='green', width=5),
                    symbol='o',
                    symbolSize=8,
                    name='Single High (~25%)'
                )
                self.chart_items.append(green_point)

            # Red B-spline for lows flow (lowest prices on left at 0x, flowing to higher prices on right at 2.25x)
            if n_lows > 2:  # Need at least 3 points for B-spline
                x_lows = [x1 + (x_50_percent - x1) * i / (n_lows - 1) for i in range(n_lows)]

                # Create B-spline interpolation with rectangle constraints
                try:
                    from scipy.interpolate import make_interp_spline
                    import numpy as np

                    # Calculate rectangle bounds for constraints (reuse from green line calculation)
                    all_prices = sorted_highs + sorted_lows
                    min_rect_price = min(all_prices)
                    max_rect_price = max(all_prices)

                    # Create smooth B-spline curve
                    x_smooth = np.linspace(x_lows[0], x_lows[-1], 100)  # 100 points for smooth curve
                    spline = make_interp_spline(x_lows, sorted_lows, k=min(3, n_lows-1))  # Cubic or lower order
                    y_smooth = spline(x_smooth)

                    # Constrain B-spline to stay within rectangle bounds
                    y_smooth = np.clip(y_smooth, min_rect_price, max_rect_price)

                    red_line = self.plot(
                        x_smooth, y_smooth,
                        pen=pg.mkPen(color='red', width=3),
                        name='Lows Flow B-spline (1%-50%)'
                    )
                    self.chart_items.append(red_line)
                except ImportError:
                    # Fallback to linear interpolation if scipy not available
                    red_line = self.plot(
                        x_lows, sorted_lows,
                        pen=pg.mkPen(color='red', width=3),
                        name='Lows Flow (1%-50%)'
                    )
                    self.chart_items.append(red_line)
            elif n_lows == 2:
                # Linear line for 2 points
                x_lows = [x1, x_50_percent]
                red_line = self.plot(
                    x_lows, sorted_lows,
                    pen=pg.mkPen(color='red', width=3),
                    name='Lows Flow (1%-50%)'
                )
                self.chart_items.append(red_line)
            elif n_lows == 1:
                # Single point at middle (1.125x representing ~25%)
                x_mid = (x1 + x_50_percent) / 2
                red_point = self.plot(
                    [x_mid], sorted_lows,
                    pen=pg.mkPen(color='red', width=5),
                    symbol='o',
                    symbolSize=8,
                    name='Single Low (~25%)'
                )
                self.chart_items.append(red_point)

        except Exception as e:
            print(f"Error drawing flow lines: {e}")

    def draw_white_lines_at_225x(self, projected_highs, projected_lows):
        """Draw white lines at 2.25x for lowest projected high and highest projected low till 2.75x."""
        try:
            if not projected_highs or not projected_lows:
                return
            lowest_projected_high = min(projected_highs)
            highest_projected_low = max(projected_lows)

            # White line for lowest projected high from 2.25x to 2.75x
            self.chart_items.append(self.plot(
                [2.25, 2.75], [lowest_projected_high, lowest_projected_high],
                pen=pg.mkPen(color='white', width=2), name='Lowest Projected High'
            ))

            # White line for highest projected low from 2.25x to 2.75x
            self.chart_items.append(self.plot(
                [2.25, 2.75], [highest_projected_low, highest_projected_low],
                pen=pg.mkPen(color='white', width=2), name='Highest Projected Low'
            ))
        except Exception as e:
            print(f"Error drawing white lines at 2.25x: {e}")

    def draw_lines_from_close_at_25x(self, current_price, projected_highs, projected_lows):
        """Draw vertical white lines at 2.5x from last close to highest projected low and lowest projected high with arrows."""
        try:
            highest_projected_low = max(projected_lows)
            lowest_projected_high = min(projected_highs)

            # Vertical line at 2.5x from last close to highest projected low
            close_to_high_low = self.plot(
                [2.5, 2.5], [current_price, highest_projected_low],  # Vertical line at 2.5x
                pen=pg.mkPen(color='white', width=2),
                name='Close to Highest Low at 2.5x'
            )
            self.chart_items.append(close_to_high_low)

            # Add arrow pointing at highest projected low - dynamic sizing, filled white
            arrow_y_size, arrow_x_size = self.calculate_arrow_sizes(projected_highs, projected_lows)
            if highest_projected_low > current_price:
                # Arrow pointing up
                arrow_high_low = self.plot(
                    [2.5, 2.5 - arrow_x_size, 2.5 + arrow_x_size, 2.5], [highest_projected_low, highest_projected_low - arrow_y_size, highest_projected_low - arrow_y_size, highest_projected_low],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Arrow to Highest Low'
                )
            else:
                # Arrow pointing down
                arrow_high_low = self.plot(
                    [2.5, 2.5 - arrow_x_size, 2.5 + arrow_x_size, 2.5], [highest_projected_low, highest_projected_low + arrow_y_size, highest_projected_low + arrow_y_size, highest_projected_low],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Arrow to Highest Low'
                )
            self.chart_items.append(arrow_high_low)

            # Vertical line at 2.5x from last close to lowest projected high
            close_to_low_high = self.plot(
                [2.5, 2.5], [current_price, lowest_projected_high],  # Vertical line at 2.5x
                pen=pg.mkPen(color='white', width=2),
                name='Close to Lowest High at 2.5x'
            )
            self.chart_items.append(close_to_low_high)

            # Add arrow pointing at lowest projected high - dynamic sizing, filled white
            if lowest_projected_high > current_price:
                # Arrow pointing up
                arrow_low_high = self.plot(
                    [2.5, 2.5 - arrow_x_size, 2.5 + arrow_x_size, 2.5], [lowest_projected_high, lowest_projected_high - arrow_y_size, lowest_projected_high - arrow_y_size, lowest_projected_high],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Arrow to Lowest High'
                )
            else:
                # Arrow pointing down
                arrow_low_high = self.plot(
                    [2.5, 2.5 - arrow_x_size, 2.5 + arrow_x_size, 2.5], [lowest_projected_high, lowest_projected_high + arrow_y_size, lowest_projected_high + arrow_y_size, lowest_projected_high],
                    pen=pg.mkPen(color='white', width=1),
                    brush=pg.mkBrush(color='white'),
                    fillLevel=0,
                    name='Arrow to Lowest High'
                )
            self.chart_items.append(arrow_low_high)

        except Exception as e:
            print(f"Error drawing lines from close at 2.5x: {e}")

    def draw_diagonal_lines_from_25x(self, projected_highs, projected_lows):
        """Draw diagonal lines from specific points at 2.5x to specific points at 0x with arrows."""
        try:
            highest_projected_low = max(projected_lows)
            lowest_projected_high = min(projected_highs)
            highest_projected_high = max(projected_highs)
            lowest_projected_low = min(projected_lows)

            # Diagonal line from highest projected low at 2.5x to highest projected high at 0x
            diag_line_1 = self.plot(
                [2.5, 0], [highest_projected_low, highest_projected_high],
                pen=pg.mkPen(color='white', width=2),
                name='Diagonal: Highest Low (2.5x) to Highest High (0x)'
            )
            self.chart_items.append(diag_line_1)

            # White diagonal line 1: from highest projected low at 2.5x to highest projected high at 0x
            diag_line_1 = self.plot(
                [2.5, 0], [highest_projected_low, highest_projected_high],
                pen=pg.mkPen(color='white', width=2),
                name='White Diagonal: Highest Low (2.5x) to Highest High (0x)'
            )
            self.chart_items.append(diag_line_1)

            # Simple arrow at 0x end of diagonal line 1 - dynamic sizing
            arrow_y_size, arrow_x_size = self.calculate_arrow_sizes(projected_highs, projected_lows)
            arrow_diag_1 = self.plot(
                [0, 0 - arrow_x_size, 0 + arrow_x_size, 0],
                [highest_projected_high, highest_projected_high - arrow_y_size, highest_projected_high - arrow_y_size, highest_projected_high],
                pen=pg.mkPen(color='white', width=1),
                brush=pg.mkBrush(color='white'),
                fillLevel=0,
                name='White Diagonal Arrow 1'
            )
            self.chart_items.append(arrow_diag_1)

            # White diagonal line 2: from lowest projected high at 2.5x to lowest projected low at 0x
            diag_line_2 = self.plot(
                [2.5, 0], [lowest_projected_high, lowest_projected_low],
                pen=pg.mkPen(color='white', width=2),
                name='White Diagonal: Lowest High (2.5x) to Lowest Low (0x)'
            )
            self.chart_items.append(diag_line_2)

            # Simple arrow at 0x end of diagonal line 2 - dynamic sizing (reuse calculated values)
            arrow_diag_2 = self.plot(
                [0, 0 - arrow_x_size, 0 + arrow_x_size, 0],
                [lowest_projected_low, lowest_projected_low + arrow_y_size, lowest_projected_low + arrow_y_size, lowest_projected_low],
                pen=pg.mkPen(color='white', width=1),
                brush=pg.mkBrush(color='white'),
                fillLevel=0,
                name='White Diagonal Arrow 2'
            )
            self.chart_items.append(arrow_diag_2)

        except Exception as e:
            print(f"Error drawing diagonal lines from 2.5x: {e}")

    def draw_price_labels_at_05x(self, current_price, highest_high, lowest_low):
        """Draw 25%, 50%, 75% price labels at 0.5x from last close to extremes."""
        try:
            # Calculate 25%, 50%, 75% from last close to highest projected high
            high_diff = highest_high - current_price
            high_25_pct = current_price + (high_diff * 0.25)
            high_50_pct = current_price + (high_diff * 0.50)
            high_75_pct = current_price + (high_diff * 0.75)

            # Calculate 25%, 50%, 75% from last close to lowest projected low
            low_diff = lowest_low - current_price
            low_25_pct = current_price + (low_diff * 0.25)
            low_50_pct = current_price + (low_diff * 0.50)
            low_75_pct = current_price + (low_diff * 0.75)

            # Add price labels for high percentages (above current price)
            if high_diff > 0:
                for pct, price in [(25, high_25_pct), (50, high_50_pct), (75, high_75_pct)]:
                    label = pg.TextItem(f'${price:.2f}', color='white', anchor=(0, 0.5))
                    label.setPos(0.5, price)
                    self.addItem(label)
                    self.text_items.append(label)

            # Add price labels for low percentages (below current price)
            if low_diff < 0:
                for pct, price in [(25, low_25_pct), (50, low_50_pct), (75, low_75_pct)]:
                    label = pg.TextItem(f'${price:.2f}', color='white', anchor=(0, 0.5))
                    label.setPos(0.5, price)
                    self.addItem(label)
                    self.text_items.append(label)

        except Exception as e:
            print(f"Error drawing price labels at 0.5x: {e}")

    def draw_dotted_lines_for_projections(self, projected_highs, projected_lows):
        """Draw light grey dotted lines between 0x to 3x for every projected high and low."""
        try:
            # Draw dotted lines for all projected highs using PyQtGraph's dotted style
            for high in projected_highs:
                if high is not None and not (isinstance(high, float) and (high != high)):  # Check for None and NaN
                    # Use PyQtGraph's built-in dotted line style
                    dotted_line_high = self.plot(
                        [0, 3], [high, high],
                        pen=pg.mkPen(color='lightgrey', width=1, style=pg.QtCore.Qt.PenStyle.DotLine),
                        name=f'Dotted High: {high:.2f}'
                    )
                    self.chart_items.append(dotted_line_high)

            # Draw dotted lines for all projected lows using PyQtGraph's dotted style
            for low in projected_lows:
                if low is not None and not (isinstance(low, float) and (low != low)):  # Check for None and NaN
                    # Use PyQtGraph's built-in dotted line style
                    dotted_line_low = self.plot(
                        [0, 3], [low, low],
                        pen=pg.mkPen(color='lightgrey', width=1, style=pg.QtCore.Qt.PenStyle.DotLine),
                        name=f'Dotted Low: {low:.2f}'
                    )
                    self.chart_items.append(dotted_line_low)

        except Exception as e:
            print(f"Error drawing dotted lines for projections: {e}")

    def draw_labels_at_25x(self, projected_highs, projected_lows):
        """Draw labels at 2.5x for standard price range and outliers."""
        try:
            highest_projected_high = max(projected_highs)
            lowest_projected_high = min(projected_highs)
            highest_projected_low = max(projected_lows)
            lowest_projected_low = min(projected_lows)

            # Standard price range: mean of highest projected low and lowest projected high
            standard_range_price = (highest_projected_low + lowest_projected_high) / 2
            standard_label = pg.TextItem(
                'Standard\nPrice\nRange',
                color='white',
                anchor=(0.5, 0.5)  # Center aligned
            )
            standard_label.setPos(2.5, standard_range_price)
            self.addItem(standard_label)
            self.text_items.append(standard_label)

            # Upper outlier: mean of highest projected high and highest projected low
            upper_outlier_price = (highest_projected_high + highest_projected_low) / 2
            upper_outlier_label = pg.TextItem(
                'Outlier',
                color='white',
                anchor=(0.5, 0.5)  # Center aligned
            )
            upper_outlier_label.setPos(2.5, upper_outlier_price)
            self.addItem(upper_outlier_label)
            self.text_items.append(upper_outlier_label)

            # Lower outlier: mean of lowest projected high and lowest projected low
            lower_outlier_price = (lowest_projected_high + lowest_projected_low) / 2
            lower_outlier_label = pg.TextItem(
                'Outlier',
                color='white',
                anchor=(0.5, 0.5)  # Center aligned
            )
            lower_outlier_label.setPos(2.5, lower_outlier_price)
            self.addItem(lower_outlier_label)
            self.text_items.append(lower_outlier_label)

        except Exception as e:
            print(f"Error drawing labels at 2.5x: {e}")

    def draw_intersection_dot(self, projected_highs, projected_lows):
        """Draw a dot at the intersection of green and red flow lines with price label."""
        try:
            # Create flow line coordinates like in fwl_odds.py
            sorted_highs = sorted(projected_highs, reverse=True)  # Highest to lowest for green line
            sorted_lows = sorted(projected_lows)  # Lowest to highest for red line

            if len(sorted_highs) < 2 or len(sorted_lows) < 2:
                return  # Need at least 2 points each to create lines

            # Create green line coordinates (1% to 50% flow)
            n_highs = len(sorted_highs)
            x1, x_50_percent = 0, 2.25
            green_x_coords = [x1 + (x_50_percent - x1) * i / (n_highs - 1) for i in range(n_highs)]
            green_y_coords = sorted_highs

            # Create red line coordinates (1% to 50% flow)
            n_lows = len(sorted_lows)
            red_x_coords = [x1 + (x_50_percent - x1) * i / (n_lows - 1) for i in range(n_lows)]
            red_y_coords = sorted_lows

            # Find intersections using line segment intersection method like fwl_odds.py
            intersections = self.find_line_intersections(
                green_x_coords, green_y_coords, red_x_coords, red_y_coords
            )

            # Draw the first intersection found within bounds
            for intersection in intersections:
                x_intersect, y_intersect = intersection

                # Only draw if intersection is within the rectangle bounds
                if 0 <= x_intersect <= 2.25:
                    # Draw intersection dot
                    intersection_dot = self.plot(
                        [x_intersect], [y_intersect],
                        pen=pg.mkPen(color='white', width=3),
                        symbol='o',
                        symbolSize=10,
                        symbolBrush=pg.mkBrush(color='white'),
                        name='Flow Lines Intersection'
                    )
                    self.chart_items.append(intersection_dot)

                    # Add price label above the dot
                    intersection_label = pg.TextItem(
                        f'${y_intersect:.2f}',
                        color='white',
                        anchor=(0.5, 1)  # Center aligned, bottom anchored (appears above dot)
                    )
                    intersection_label.setPos(x_intersect, y_intersect)
                    self.addItem(intersection_label)
                    self.text_items.append(intersection_label)
                    break  # Only draw the first valid intersection

        except Exception as e:
            print(f"Error drawing intersection dot: {e}")

    def find_line_intersections(self, x1, y1, x2, y2):
        """Find intersection points between two lines defined by sets of points (adapted from fwl_odds.py)."""
        intersections = []

        try:
            # For each line segment in the first line (green)
            for i in range(len(x1) - 1):
                # Get the current line segment from the green line
                x1_start, y1_start = x1[i], y1[i]
                x1_end, y1_end = x1[i + 1], y1[i + 1]

                # For each line segment in the second line (red)
                for j in range(len(x2) - 1):
                    # Get the current line segment from the red line
                    x2_start, y2_start = x2[j], y2[j]
                    x2_end, y2_end = x2[j + 1], y2[j + 1]

                    # Find intersection between these two line segments
                    intersection = self.line_segment_intersection(
                        x1_start, y1_start, x1_end, y1_end,
                        x2_start, y2_start, x2_end, y2_end
                    )

                    if intersection is not None:
                        intersections.append(intersection)

        except Exception as e:
            print(f"Error in find_line_intersections: {e}")

        return intersections

    def line_segment_intersection(self, x1, y1, x2, y2, x3, y3, x4, y4):
        """Find intersection point between two line segments (adapted from fwl_odds.py)."""
        try:
            # Calculate the direction vectors
            denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)

            # If denominator is 0, lines are parallel
            if abs(denom) < 1e-10:
                return None

            # Calculate intersection parameters
            t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
            u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom

            # Check if intersection is within both line segments
            if 0 <= t <= 1 and 0 <= u <= 1:
                # Calculate intersection point
                intersection_x = x1 + t * (x2 - x1)
                intersection_y = y1 + t * (y2 - y1)
                return (intersection_x, intersection_y)

            return None

        except Exception as e:
            print(f"Error in line_segment_intersection: {e}")
            return None

    def draw_stdev_markers(self, current_price, highest_high, lowest_low, x1, x2):
        """Draw 25%, 50%, 75% standard deviation markers from current price to extremes (0x-3x coords)."""
        try:

            # Calculate standard deviations from current price to highest high
            high_diff = highest_high - current_price
            high_25_pct = current_price + (high_diff * 0.25)
            high_50_pct = current_price + (high_diff * 0.50)
            high_75_pct = current_price + (high_diff * 0.75)

            # Calculate standard deviations from current price to lowest low
            low_diff = lowest_low - current_price
            low_25_pct = current_price + (low_diff * 0.25)
            low_50_pct = current_price + (low_diff * 0.50)
            low_75_pct = current_price + (low_diff * 0.75)

            # Draw horizontal lines for high standard deviations (above current price)
            if high_diff > 0:
                for pct, price in [(25, high_25_pct), (50, high_50_pct), (75, high_75_pct)]:
                    # Use simple solid lines for now
                    line = self.plot(
                        [x1, x2], [price, price],
                        pen=pg.mkPen(color='lightgreen', width=1),
                        name=f'High {pct}% StDev'
                    )
                    self.chart_items.append(line)

                    # Add label positioned just outside the rectangle at 3.2x
                    label = pg.TextItem(f'{pct}%', color='lightgreen', anchor=(0, 0.5))
                    label.setPos(3.2, price)
                    self.addItem(label)
                    self.text_items.append(label)

            # Draw horizontal lines for low standard deviations (below current price)
            if low_diff < 0:
                for pct, price in [(25, low_25_pct), (50, low_50_pct), (75, low_75_pct)]:
                    # Use simple solid lines for now
                    line = self.plot(
                        [x1, x2], [price, price],
                        pen=pg.mkPen(color='lightcoral', width=1),
                        name=f'Low {pct}% StDev'
                    )
                    self.chart_items.append(line)

                    # Add label positioned just outside the rectangle at 3.2x
                    label = pg.TextItem(f'{pct}%', color='lightcoral', anchor=(0, 0.5))
                    label.setPos(3.2, price)
                    self.addItem(label)
                    self.text_items.append(label)

        except Exception as e:
            print(f"Error drawing standard deviation markers: {e}")

    def add_price_labels(self, prices, x_position, label_type):
        """Add price labels at the specified x position."""
        try:
            for price in prices:
                # Create text label
                label_text = f"{price:.2f}"
                text_item = pg.TextItem(
                    text=label_text,
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned, vertically centered
                )
                text_item.setPos(x_position, price)

                # Add to chart
                self.addItem(text_item)
                self.text_items.append(text_item)

        except Exception as e:
            print(f"Error adding {label_type} labels: {e}")

    def add_odds_labels(self, prices, x_position, label_type):
        """Add odds percentage labels at the specified x position."""
        try:
            if not prices:
                return

            # Sort prices for consistent odds calculation
            sorted_prices = sorted(prices)

            # Calculate odds based on rank/position, not price value
            n_prices = len(sorted_prices)

            for price in prices:
                if n_prices == 1:
                    percentage = 25.5  # Single value gets midpoint
                else:
                    # Find the rank/position of this price in the sorted list
                    price_rank = sorted_prices.index(price)

                    if label_type == "High":
                        # For highs: highest (last in sorted list) = 1%, lowest (first in sorted list) = 50%
                        # Reverse the rank so highest price gets rank 0
                        reversed_rank = n_prices - 1 - price_rank
                        ratio = reversed_rank / (n_prices - 1)
                        percentage = 1.0 + (ratio * 49.0)  # 1% to 50%
                    else:  # label_type == "Low"
                        # For lows: lowest (first in sorted list) = 1%, highest (last in sorted list) = 50%
                        ratio = price_rank / (n_prices - 1)
                        percentage = 1.0 + (ratio * 49.0)  # 1% to 50%

                # Create odds label
                label_text = f"{percentage:.1f}%"
                text_item = pg.TextItem(
                    text=label_text,
                    color='white',
                    anchor=(0, 0.5)  # Left-aligned, vertically centered
                )
                text_item.setPos(x_position, price)

                # Add to chart
                self.addItem(text_item)
                self.text_items.append(text_item)

        except Exception as e:
            print(f"Error adding {label_type} odds labels: {e}")

    def draw_last_close_line(self, last_close):
        """Draw a white dotted line at the last close price from -0.5x to 7.5x with current price label."""
        try:
            # Create line data points (start from -0.5x instead of 0x)
            line_x = [-0.5, 7.5]
            line_y = [last_close, last_close]

            # Create dotted line
            line_item = self.plot(
                line_x, line_y,
                pen=pg.mkPen(color='white', width=1, style=pg.QtCore.Qt.PenStyle.DotLine),
                name='Last Close'
            )
            self.chart_items.append(line_item)

            # Add current price label (left aligned above the line)
            current_price_label = pg.TextItem(
                f'Current Price:\n${last_close:.2f}',
                color='white',
                anchor=(0, 1)  # Left aligned, bottom anchored (so it appears above the line)
            )
            current_price_label.setPos(-0.5, last_close)  # Position at start of line
            self.addItem(current_price_label)
            self.text_items.append(current_price_label)

        except Exception as e:
            print(f"Error drawing last close line: {e}")

    def draw_arrow_lines(self, projected_highs, projected_lows, current_price):
        """Draw vertical lines at 5x and 7x positions that go to the current price level."""
        try:
            # Find highest and lowest projected highs
            highest_projected_high = max(projected_highs)
            lowest_projected_high = min(projected_highs)

            # Find highest and lowest projected lows
            highest_projected_low = max(projected_lows)
            lowest_projected_low = min(projected_lows)

            # At 5x: Red vertical line from highest projected high to current price level
            red_line_5x = self.plot(
                [5, 5], [highest_projected_high, current_price],
                pen=pg.mkPen(color='red', width=2),
                name=f'Red Line 5x: {highest_projected_high:.2f} to {current_price:.2f}'
            )
            self.chart_items.append(red_line_5x)

            # Add filled red arrow pointing at current price (at 5x position) - dynamic sizing
            arrow_y_size, arrow_x_size = self.calculate_arrow_sizes(projected_highs, projected_lows)
            if highest_projected_high > current_price:
                # Arrow pointing down (line goes from high to low)
                red_arrow_5x = self.plot(
                    [5, 5 - arrow_x_size, 5 + arrow_x_size, 5], [current_price, current_price + arrow_y_size, current_price + arrow_y_size, current_price],
                    pen=pg.mkPen(color='red', width=1),
                    brush=pg.mkBrush(color='red'),
                    fillLevel=0,
                    name='Red Arrow 5x'
                )
            else:
                # Arrow pointing up (line goes from low to high)
                red_arrow_5x = self.plot(
                    [5, 5 - arrow_x_size, 5 + arrow_x_size, 5], [current_price, current_price - arrow_y_size, current_price - arrow_y_size, current_price],
                    pen=pg.mkPen(color='red', width=1),
                    brush=pg.mkBrush(color='red'),
                    fillLevel=0,
                    name='Red Arrow 5x'
                )
            self.chart_items.append(red_arrow_5x)

            # At 5x: Green vertical line from lowest projected high to current price level
            green_line_5x = self.plot(
                [5, 5], [lowest_projected_high, current_price],
                pen=pg.mkPen(color='green', width=2),
                name=f'Green Line 5x: {lowest_projected_high:.2f} to {current_price:.2f}'
            )
            self.chart_items.append(green_line_5x)

            # Add filled green arrow pointing at current price (at 5x position) - dynamic sizing (reuse calculated values)
            if lowest_projected_high > current_price:
                # Arrow pointing down (line goes from high to low)
                green_arrow_5x = self.plot(
                    [5, 5 - arrow_x_size, 5 + arrow_x_size, 5], [current_price, current_price + arrow_y_size, current_price + arrow_y_size, current_price],
                    pen=pg.mkPen(color='green', width=1),
                    brush=pg.mkBrush(color='green'),
                    fillLevel=0,
                    name='Green Arrow 5x'
                )
            else:
                # Arrow pointing up (line goes from low to high)
                green_arrow_5x = self.plot(
                    [5, 5 - arrow_x_size, 5 + arrow_x_size, 5], [current_price, current_price - arrow_y_size, current_price - arrow_y_size, current_price],
                    pen=pg.mkPen(color='green', width=1),
                    brush=pg.mkBrush(color='green'),
                    fillLevel=0,
                    name='Green Arrow 5x'
                )
            self.chart_items.append(green_arrow_5x)

            # At 7x: Red vertical line from lowest projected low to current price level
            red_line_7x = self.plot(
                [7, 7], [lowest_projected_low, current_price],
                pen=pg.mkPen(color='red', width=2),
                name=f'Red Line 7x: {lowest_projected_low:.2f} to {current_price:.2f}'
            )
            self.chart_items.append(red_line_7x)

            # Add filled red arrow pointing at current price (at 7x position) - dynamic sizing (reuse calculated values)
            if lowest_projected_low > current_price:
                # Arrow pointing down (line goes from high to low)
                red_arrow_7x = self.plot(
                    [7, 7 - arrow_x_size, 7 + arrow_x_size, 7], [current_price, current_price + arrow_y_size, current_price + arrow_y_size, current_price],
                    pen=pg.mkPen(color='red', width=1),
                    brush=pg.mkBrush(color='red'),
                    fillLevel=0,
                    name='Red Arrow 7x'
                )
            else:
                # Arrow pointing up (line goes from low to high)
                red_arrow_7x = self.plot(
                    [7, 7 - arrow_x_size, 7 + arrow_x_size, 7], [current_price, current_price - arrow_y_size, current_price - arrow_y_size, current_price],
                    pen=pg.mkPen(color='red', width=1),
                    brush=pg.mkBrush(color='red'),
                    fillLevel=0,
                    name='Red Arrow 7x'
                )
            self.chart_items.append(red_arrow_7x)

            # At 7x: Green vertical line from highest projected low to current price level
            green_line_7x = self.plot(
                [7, 7], [highest_projected_low, current_price],
                pen=pg.mkPen(color='green', width=2),
                name=f'Green Line 7x: {highest_projected_low:.2f} to {current_price:.2f}'
            )
            self.chart_items.append(green_line_7x)

            # Add filled green arrow pointing at current price (at 7x position) - dynamic sizing (reuse calculated values)
            if highest_projected_low > current_price:
                # Arrow pointing down (line goes from high to low)
                green_arrow_7x = self.plot(
                    [7, 7 - arrow_x_size, 7 + arrow_x_size, 7], [current_price, current_price + arrow_y_size, current_price + arrow_y_size, current_price],
                    pen=pg.mkPen(color='green', width=1),
                    brush=pg.mkBrush(color='green'),
                    fillLevel=0,
                    name='Green Arrow 7x'
                )
            else:
                # Arrow pointing up (line goes from low to high)
                green_arrow_7x = self.plot(
                    [7, 7 - arrow_x_size, 7 + arrow_x_size, 7], [current_price, current_price - arrow_y_size, current_price - arrow_y_size, current_price],
                    pen=pg.mkPen(color='green', width=1),
                    brush=pg.mkBrush(color='green'),
                    fillLevel=0,
                    name='Green Arrow 7x'
                )
            self.chart_items.append(green_arrow_7x)

        except Exception as e:
            print(f"Error drawing arrow lines: {e}")

    def set_axis_ranges(self, projected_highs, projected_lows, extend_x=8.0):
        """Set appropriate axis ranges for the chart."""
        try:
            # Calculate price range with some padding
            all_prices = projected_highs + projected_lows
            min_price = min(all_prices)
            max_price = max(all_prices)
            price_range = max_price - min_price
            padding = price_range * 0.1  # 10% padding

            # Set Y axis range
            self.setYRange(min_price - padding, max_price + padding)

            # Set X axis range to show from 0 to extend_x (covers all elements including dotted line)
            self.setXRange(0, extend_x)

        except Exception as e:
            print(f"Error setting axis ranges: {e}")


class VolatilityStatisticsTab(QWidget):
    """Volatility Statistics tab with unified state management across all subtabs."""

    # Class variables to track settings state across all instances
    _settings_expanded = False
    _all_settings_buttons = []
    _all_settings_content = []

    # Unified filter state management across all subtabs
    _unified_filter_type = 0  # Default to H/L matching (0 = H/L matching, 1 = weekday matching, -1 = no filter)
    _all_filter_button_groups = []  # Track all filter button groups for synchronization
    _all_volatility_tabs = []  # Track all volatility tab instances



    def __init__(self, data_service=None, parent=None):
        super().__init__(parent)
        self.data_service = data_service

        # Options service will be accessed through data_service

        # Add this instance to the global list for unified state management
        VolatilityStatisticsTab._all_volatility_tabs.append(self)

        # Store original data for filtering
        self._original_high_data = []
        self._original_low_data = []

        # Shared options data cache for all charts in this tab instance
        self._shared_options_data = None
        self._shared_options_timestamp = 0

        # Initialize FWL Aggr debounce timer
        self.fwl_aggr_timer = QTimer()
        self.fwl_aggr_timer.setSingleShot(True)
        self.fwl_aggr_timer.timeout.connect(self._trigger_data_refresh)
        self._main_window_ref = None

        self.setup_ui()

    def setup_ui(self):
        """Initialize the user interface with unified state management across subtabs."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Create main horizontal splitter for content and shared settings
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setChildrenCollapsible(False)
        main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #2b2b2b;
                width: 2px;
            }
            QSplitter::handle:hover {
                background-color: #2b2b2b;
            }
        """)

        # Create tab widget for left side (3/4 width)
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #2b2b2b;
                background-color: #1e1e1e;
                border-top: none;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #1e1e1e;
                color: #ffffff;
                border: none;
                padding: 4px 12px;
                margin-right: 2px;
                min-width: 60px;
                max-height: 24px;
            }
            QTabBar::tab:selected {
                background-color: #2b2b2b;
                border: 1px solid #1e1e1e;
                border-bottom: none;
            }
            QTabBar::tab:hover:!selected {
                background-color: #3c3c3c;
            }
        """)

        # Set the tab bar height to be smaller
        self.tab_widget.tabBar().setFixedHeight(26)

        # Create Chart tab (no internal settings panel)
        self.chart_tab = self.create_chart_tab()
        self.tab_widget.addTab(self.chart_tab, "Chart")

        # Create Data tab (no internal settings panel)
        self.data_tab = self.create_data_tab()
        self.tab_widget.addTab(self.data_tab, "Data")

        # Create unified shared settings panel for right side (1/4 width)
        self.unified_settings_widget = self.create_unified_settings_panel()

        # Add widgets to main splitter
        main_splitter.addWidget(self.tab_widget)
        main_splitter.addWidget(self.unified_settings_widget)

        # Set the sizes for 3/4 and 1/4 split
        main_splitter.setSizes([750, 250])
        main_splitter.setStretchFactor(0, 3)
        main_splitter.setStretchFactor(1, 1)

        # Disable handle movement for fixed split
        handle = main_splitter.handle(1)
        handle.setEnabled(False)

        layout.addWidget(main_splitter)

    def create_unified_settings_panel(self):
        """Create the unified settings panel that controls all subtabs."""
        # Create container widget with top margin to align with tab content area
        container_widget = QWidget()
        container_layout = QVBoxLayout(container_widget)
        container_layout.setContentsMargins(0, 26, 0, 0)  # 26px top margin to match tab bar height
        container_layout.setSpacing(0)

        settings_widget = QWidget()
        settings_widget.setStyleSheet("border: 2px solid #2b2b2b; background-color: #1e1e1e;")

        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Create collapsible settings button
        self.unified_settings_button = QPushButton("▶ Settings")
        self.unified_settings_button.setFixedHeight(30)
        self.unified_settings_button.setFont(QFont("Segoe UI", 10))

        # Style the settings button to match dark theme with left border
        self.unified_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-left: 3px solid #555555;
                text-align: left;
                padding-left: 10px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        # Create settings content area (initially hidden)
        self.unified_settings_content = QWidget()
        self.unified_settings_content.setVisible(self._settings_expanded)

        # Settings content layout with 1/2 horizontal split
        settings_layout = QVBoxLayout()
        settings_layout.setContentsMargins(5, 5, 5, 5)
        settings_layout.setSpacing(0)

        # Create horizontal splitter for settings 1/2 split (left/right)
        settings_splitter = QSplitter(Qt.Orientation.Horizontal)
        settings_splitter.setChildrenCollapsible(False)
        settings_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #555555;
                width: 2px;
            }
        """)

        # Left section (1/2) - FWL Aggr. setting
        left_settings = QWidget()
        left_settings.setStyleSheet("background-color: #1e1e1e;")
        left_layout = QVBoxLayout()
        left_layout.setContentsMargins(8, 8, 8, 8)
        left_layout.setSpacing(8)

        # Add Aggregation title
        aggregation_title = QLabel("Aggregation")
        aggregation_title.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        aggregation_title.setStyleSheet("color: #ffffff; margin-bottom: 5px;")
        aggregation_title.setFixedHeight(25)  # Fixed height for alignment
        left_layout.addWidget(aggregation_title)

        # FWL Aggr. setting
        fwl_aggr_layout = QHBoxLayout()
        fwl_aggr_layout.setSpacing(5)
        fwl_aggr_layout.setContentsMargins(0, 0, 0, 0)

        # FWL Aggr. label - match radio button styling
        fwl_aggr_label = QLabel("FWL Aggr.:")
        fwl_aggr_label.setFont(QFont("Segoe UI", 9))
        fwl_aggr_label.setStyleSheet("color: #ffffff; border: none; outline: none;")
        fwl_aggr_label.setAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft)
        fwl_aggr_label.setMinimumHeight(20)  # Match radio button height
        fwl_aggr_label.setFixedWidth(120)  # Wider to match occurrence limiter

        # FWL Aggr. custom number input with up/down buttons
        fwl_input_container = QWidget()
        fwl_input_layout = QHBoxLayout()
        fwl_input_layout.setContentsMargins(0, 0, 0, 0)
        fwl_input_layout.setSpacing(2)

        # Text input field
        self.fwl_aggr_input = QLineEdit()
        self.fwl_aggr_input.setText("1")  # Default value
        self.fwl_aggr_input.setFixedSize(25, 18)  # Smaller and more compact
        self.fwl_aggr_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        input_font = QFont("Segoe UI", 8)  # Smaller font
        self.fwl_aggr_input.setFont(input_font)

        # Up/Down button container
        button_container = QWidget()
        button_layout = QVBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(0)

        # Up button
        self.fwl_up_button = QPushButton("▲")
        self.fwl_up_button.setFixedSize(14, 9)  # Even smaller buttons
        button_font = QFont("Segoe UI", 6)  # Slightly larger font for readability
        self.fwl_up_button.setFont(button_font)

        # Down button
        self.fwl_down_button = QPushButton("▼")
        self.fwl_down_button.setFixedSize(14, 9)  # Even smaller buttons
        self.fwl_down_button.setFont(button_font)

        # Add buttons to button layout
        button_layout.addWidget(self.fwl_up_button)
        button_layout.addWidget(self.fwl_down_button)
        button_container.setLayout(button_layout)

        # Add input and buttons to container
        fwl_input_layout.addWidget(self.fwl_aggr_input)
        fwl_input_layout.addWidget(button_container)
        fwl_input_container.setLayout(fwl_input_layout)
        fwl_input_container.setFixedSize(45, 18)  # Compact container to match input height

        # Connect button signals
        self.fwl_up_button.clicked.connect(self._increment_fwl_aggr)
        self.fwl_down_button.clicked.connect(self._decrement_fwl_aggr)

        # Connect text change to validation and data refresh
        self.fwl_aggr_input.textChanged.connect(self._validate_fwl_aggr_input)
        self.fwl_aggr_input.textChanged.connect(self._on_fwl_aggr_changed)

        # Add to horizontal layout
        fwl_aggr_layout.addWidget(fwl_aggr_label)
        fwl_aggr_layout.addWidget(fwl_input_container)
        fwl_aggr_layout.addStretch()  # Push to left

        # Add to left layout
        left_layout.addLayout(fwl_aggr_layout)

        # Occurrence Limiter setting
        occurrence_layout = QHBoxLayout()
        occurrence_layout.setSpacing(5)
        occurrence_layout.setContentsMargins(0, 0, 0, 0)

        # Occurrence Limiter label - match radio button styling
        occurrence_label = QLabel("Occurrence Limiter:")
        occurrence_label.setFont(QFont("Segoe UI", 9))
        occurrence_label.setStyleSheet("color: #ffffff; border: none; outline: none;")
        occurrence_label.setAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft)
        occurrence_label.setMinimumHeight(20)  # Match radio button height
        occurrence_label.setFixedWidth(120)  # Match FWL Aggr label width

        # Occurrence Limiter custom number input with up/down buttons
        occurrence_input_container = QWidget()
        occurrence_input_layout = QHBoxLayout()
        occurrence_input_layout.setContentsMargins(0, 0, 0, 0)
        occurrence_input_layout.setSpacing(2)

        # Text input field
        self.occurrence_input = QLineEdit()
        self.occurrence_input.setText("0")  # Default value (0 = all rows)
        self.occurrence_input.setFixedSize(25, 18)  # Match FWL Aggr input size
        self.occurrence_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.occurrence_input.setFont(input_font)

        # Up/Down button container for occurrence
        occurrence_button_container = QWidget()
        occurrence_button_layout = QVBoxLayout()
        occurrence_button_layout.setContentsMargins(0, 0, 0, 0)
        occurrence_button_layout.setSpacing(0)

        # Up button for occurrence
        self.occurrence_up_button = QPushButton("▲")
        self.occurrence_up_button.setFixedSize(14, 9)  # Match FWL Aggr button size
        self.occurrence_up_button.setFont(button_font)

        # Down button for occurrence
        self.occurrence_down_button = QPushButton("▼")
        self.occurrence_down_button.setFixedSize(14, 9)  # Match FWL Aggr button size
        self.occurrence_down_button.setFont(button_font)

        # Add buttons to occurrence button layout
        occurrence_button_layout.addWidget(self.occurrence_up_button)
        occurrence_button_layout.addWidget(self.occurrence_down_button)
        occurrence_button_container.setLayout(occurrence_button_layout)

        # Add input and buttons to occurrence container
        occurrence_input_layout.addWidget(self.occurrence_input)
        occurrence_input_layout.addWidget(occurrence_button_container)
        occurrence_input_container.setLayout(occurrence_input_layout)
        occurrence_input_container.setFixedSize(45, 18)  # Match FWL Aggr container size

        # Connect occurrence button signals
        self.occurrence_up_button.clicked.connect(self._increment_occurrence)
        self.occurrence_down_button.clicked.connect(self._decrement_occurrence)

        # Connect text change to validation and data refresh
        self.occurrence_input.textChanged.connect(self._validate_occurrence_input)
        self.occurrence_input.textChanged.connect(self._on_occurrence_changed)

        # Add to horizontal layout
        occurrence_layout.addWidget(occurrence_label)
        occurrence_layout.addWidget(occurrence_input_container)
        occurrence_layout.addStretch()  # Push to left

        # Add to left layout
        left_layout.addLayout(occurrence_layout)
        left_layout.addStretch()  # Push to top

        left_settings.setLayout(left_layout)

        # Right section (1/2) - unified filter radio buttons
        right_settings = QWidget()
        right_settings.setStyleSheet("background-color: #1e1e1e;")
        right_layout = QVBoxLayout()
        right_layout.setContentsMargins(8, 8, 8, 8)
        right_layout.setSpacing(8)

        # Add Market Condition Filter title
        filter_title = QLabel("Market Condition Filter")
        filter_title.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        filter_title.setStyleSheet("color: #ffffff; margin-bottom: 5px;")
        filter_title.setFixedHeight(25)  # Fixed height for alignment
        right_layout.addWidget(filter_title)

        # Create unified button group for exclusive selection
        self.unified_filter_button_group = QButtonGroup()

        # Create radio buttons
        self.hl_matching_radio = QRadioButton("H/L Matching")
        self.weekday_matching_radio = QRadioButton("Weekday Matching")

        # Set font and styling
        radio_font = QFont("Segoe UI", 9)
        self.hl_matching_radio.setFont(radio_font)
        self.weekday_matching_radio.setFont(radio_font)

        # Style radio buttons for dark theme
        filter_radio_style = """
            QRadioButton {
                color: #ffffff;
                border: none;
                outline: none;
                spacing: 3px;
            }
            QRadioButton::indicator {
                width: 12px;
                height: 12px;
            }
            QRadioButton::indicator:unchecked {
                border: 1px solid #555555;
                border-radius: 6px;
                background-color: #1e1e1e;
            }
            QRadioButton::indicator:checked {
                border: 1px solid #ffffff;
                border-radius: 6px;
                background-color: #555555;
            }
        """

        self.hl_matching_radio.setStyleSheet(filter_radio_style)
        self.weekday_matching_radio.setStyleSheet(filter_radio_style)

        # Set H/L Matching as default based on unified state
        if self._unified_filter_type == 0:
            self.hl_matching_radio.setChecked(True)
        elif self._unified_filter_type == 1:
            self.weekday_matching_radio.setChecked(True)

        # Add buttons to group
        self.unified_filter_button_group.addButton(self.hl_matching_radio, 0)
        self.unified_filter_button_group.addButton(self.weekday_matching_radio, 1)

        # Connect to unified filter function
        self.hl_matching_radio.toggled.connect(self.on_unified_filter_changed)
        self.weekday_matching_radio.toggled.connect(self.on_unified_filter_changed)

        # Add this button group to the global list for synchronization
        VolatilityStatisticsTab._all_filter_button_groups.append(self.unified_filter_button_group)

        # Add radio buttons to right layout
        right_layout.addWidget(self.hl_matching_radio)
        right_layout.addWidget(self.weekday_matching_radio)
        right_layout.addStretch()  # Push buttons to top

        right_settings.setLayout(right_layout)

        # Add widgets to splitter
        settings_splitter.addWidget(left_settings)
        settings_splitter.addWidget(right_settings)

        # Set equal sizes for 1/2 split - use larger numbers for better precision
        settings_splitter.setSizes([1000, 1000])
        settings_splitter.setStretchFactor(0, 1)
        settings_splitter.setStretchFactor(1, 1)

        # Disable handle movement for fixed split
        settings_handle = settings_splitter.handle(1)
        settings_handle.setEnabled(False)

        settings_layout.addWidget(settings_splitter)

        # Add Zones section within settings content
        zones_container = QWidget()
        zones_container.setStyleSheet("background-color: #1e1e1e; margin-top: 10px;")
        zones_layout = QVBoxLayout()
        zones_layout.setContentsMargins(8, 8, 8, 8)
        zones_layout.setSpacing(8)

        # Add Zones title
        zones_title = QLabel("Zones")
        zones_title.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        zones_title.setStyleSheet("color: #ffffff; margin-bottom: 8px; padding: 4px 0px;")
        zones_title.adjustSize()  # Auto-size to fit text content
        zones_layout.addWidget(zones_title)

        # Create zones viewer button
        self.zones_viewer_button = QPushButton("View Zone Prices")
        self.zones_viewer_button.setFixedHeight(30)
        self.zones_viewer_button.setFont(QFont("Segoe UI", 9))

        # Style the zones viewer button to match other controls in settings
        self.zones_viewer_button.setStyleSheet("""
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                text-align: center;
                padding: 3px 8px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        # Connect zones viewer button to handler
        self.zones_viewer_button.clicked.connect(self._on_zones_viewer_button_clicked)
        zones_layout.addWidget(self.zones_viewer_button)
        zones_layout.addStretch()  # Push content to top

        zones_container.setLayout(zones_layout)
        settings_layout.addWidget(zones_container)

        self.unified_settings_content.setLayout(settings_layout)

        # Add to class lists for shared state management
        self._all_settings_buttons.append(self.unified_settings_button)
        self._all_settings_content.append(self.unified_settings_content)

        # Connect button click to toggle function
        self.unified_settings_button.clicked.connect(self.toggle_settings)

        # Add widgets to layout
        layout.addWidget(self.unified_settings_button)
        layout.addWidget(self.unified_settings_content)

        # Add Statistics section below Settings (NOT part of collapsible Settings content)
        statistics_title = QLabel("Statistics")
        statistics_title.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        statistics_title.setStyleSheet("color: #ffffff; margin-bottom: 8px; margin-top: 5px; padding: 2px;")
        statistics_title.setMinimumHeight(25)  # Reduce height to move content up
        layout.addWidget(statistics_title)

        # Create main statistics container
        statistics_container = QWidget()
        statistics_container.setStyleSheet("QWidget { border: none; background-color: transparent; }")
        statistics_container_layout = QVBoxLayout()
        statistics_container_layout.setContentsMargins(0, 0, 0, 10)  # Add bottom margin like charts
        statistics_container_layout.setSpacing(10)

        # Create single scrollable area for both volatility and projected statistics
        from PyQt6.QtWidgets import QScrollArea
        combined_scroll_area = QScrollArea()
        combined_scroll_area.setWidgetResizable(True)
        combined_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        combined_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        combined_scroll_area.setStyleSheet("QScrollArea { border: none; background-color: transparent; }")

        # Widget to hold all statistics (both volatility and projected)
        self.combined_statistics_widget = QWidget()
        self.combined_statistics_widget.setStyleSheet("QWidget { border: none; background-color: transparent; }")
        self.combined_statistics_layout = QVBoxLayout()
        self.combined_statistics_layout.setContentsMargins(2, 2, 2, 2)
        self.combined_statistics_layout.setSpacing(1)
        self.combined_statistics_widget.setLayout(self.combined_statistics_layout)

        combined_scroll_area.setWidget(self.combined_statistics_widget)
        statistics_container_layout.addWidget(combined_scroll_area, 1)  # Give scroll area stretch factor to expand

        statistics_container.setLayout(statistics_container_layout)
        layout.addWidget(statistics_container, 1)  # Give it stretch factor to fill remaining space

        layout.addStretch()  # Push content to top

        settings_widget.setLayout(layout)

        # Add settings widget to container with top margin
        container_layout.addWidget(settings_widget)

        return container_widget



    def toggle_settings(self):
        """Toggle the visibility of unified settings content."""
        # Toggle the class variable
        self._settings_expanded = not self._settings_expanded

        # Update unified settings button and content
        if hasattr(self, 'unified_settings_button'):
            if self._settings_expanded:
                self.unified_settings_button.setText("▼ Settings")  # Expanded
            else:
                self.unified_settings_button.setText("▶ Settings")  # Collapsed
            self.unified_settings_button.setFont(QFont("Segoe UI", 10))

        if hasattr(self, 'unified_settings_content'):
            self.unified_settings_content.setVisible(self._settings_expanded)

    def on_unified_filter_changed(self):
        """Handle unified filter changes that affect all subtabs."""
        if not hasattr(self, 'unified_filter_button_group'):
            return

        # Get the checked button
        checked_button = self.unified_filter_button_group.checkedButton()
        if not checked_button:
            new_filter_type = -1  # No filter
        else:
            # Get the button ID (0 = H/L Matching, 1 = Weekday Matching)
            new_filter_type = self.unified_filter_button_group.id(checked_button)

        # Update unified filter state
        VolatilityStatisticsTab._unified_filter_type = new_filter_type

        # Synchronize all filter button groups across all instances
        self.synchronize_all_filter_states(new_filter_type)

        # Apply filter to all volatility tab instances
        self.apply_unified_filter_to_all_tabs()

    @classmethod
    def synchronize_all_filter_states(cls, filter_type):
        """Synchronize filter states across all volatility tab instances."""
        for button_group in cls._all_filter_button_groups:
            # Temporarily disconnect signals to avoid recursion
            for button in button_group.buttons():
                button.blockSignals(True)

            # Update button states
            if filter_type == 0:  # H/L Matching
                button_group.button(0).setChecked(True)
            elif filter_type == 1:  # Weekday Matching
                button_group.button(1).setChecked(True)
            else:  # No filter
                for button in button_group.buttons():
                    button.setChecked(False)

            # Re-enable signals
            for button in button_group.buttons():
                button.blockSignals(False)

    @classmethod
    def apply_unified_filter_to_all_tabs(cls):
        """Apply the unified filter to all volatility tab instances."""
        for tab_instance in cls._all_volatility_tabs:
            if hasattr(tab_instance, '_original_high_data') and hasattr(tab_instance, '_original_low_data'):
                tab_instance.apply_unified_filter()

    def apply_unified_filter(self):
        """Apply the unified filter using backend processing - affects all subtabs."""
        if not self._original_high_data or not self._original_low_data:
            return

        if not self.data_service:
            return

        try:
            # Request filtered data from backend using unified filter state
            filter_result = self.data_service.apply_volatility_statistics_filter(
                self._original_high_data, self._original_low_data, self._unified_filter_type
            )

            # Get filtered data from backend
            filtered_high = filter_result.get("volatility_filtered_high_data", [])
            filtered_low = filter_result.get("volatility_filtered_low_data", [])

            # Apply occurrence limit if set
            occurrence_count = self.get_occurrence_count()
            if occurrence_count > 0:
                # Limit to latest N occurrences (chronologically)
                # The data is already sorted chronologically, so take the last N items
                if len(filtered_high) > occurrence_count:
                    filtered_high = filtered_high[-occurrence_count:]
                if len(filtered_low) > occurrence_count:
                    filtered_low = filtered_low[-occurrence_count:]

            # Update both high and low tables if they exist
            if hasattr(self, 'high_table_model'):
                self.high_table_model.beginResetModel()
                self.high_table_model._data = filtered_high
                self.high_table_model.endResetModel()

            if hasattr(self, 'low_table_model'):
                self.low_table_model.beginResetModel()
                self.low_table_model._data = filtered_low
                self.low_table_model.endResetModel()

            # Update charts with the same filtered data and market data
            market_data = getattr(self, '_market_data', None)
            self.update_charts_with_filtered_data(filtered_high, filtered_low, market_data)

        except Exception as e:
            print(f"Error applying unified volatility statistics filter: {e}")
            # Fallback: show unfiltered data
            if hasattr(self, 'high_table_model'):
                self.high_table_model.beginResetModel()
                self.high_table_model._data = self._original_high_data
                self.high_table_model.endResetModel()

            if hasattr(self, 'low_table_model'):
                self.low_table_model.beginResetModel()
                self.low_table_model._data = self._original_low_data
                self.low_table_model.endResetModel()

    def switch_chart(self, button):
        """Switch between different chart types based on radio button selection."""
        if hasattr(self, 'chart_stack'):
            button_id = self.button_group.id(button)
            self.chart_stack.setCurrentIndex(button_id)

            # Show/hide options header based on chart selection
            if hasattr(self, 'options_header'):
                # Show options header only for density chart (index 1)
                self.options_header.setVisible(button_id == 1)

    def create_header_with_radio_buttons(self):
        """Create small header with 3 central radio buttons."""
        header_widget = QWidget()
        header_widget.setFixedHeight(30)
        header_widget.setStyleSheet("background-color: #2b2b2b;")
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(5, 2, 5, 2)
        header_layout.setSpacing(10)

        # Create button group for exclusive selection
        self.button_group = QButtonGroup()

        # Create radio buttons with smaller font
        volatility_radio = QRadioButton("Volatility Graph")
        density_radio = QRadioButton("Density Graph")
        fwl_radio = QRadioButton("FWL Odds")

        # Set font size using PyQt6
        font = QFont("Segoe UI", 10)
        volatility_radio.setFont(font)
        density_radio.setFont(font)
        fwl_radio.setFont(font)

        # Style radio buttons to match dark theme (minimal CSS for radio buttons only)
        radio_style = """
            QRadioButton {
                color: #ffffff;
                border: none;
                outline: none;
            }
            QRadioButton::indicator {
                width: 14px;
                height: 14px;
            }
            QRadioButton::indicator:unchecked {
                border: 1px solid #555555;
                border-radius: 7px;
                background-color: #1e1e1e;
            }
            QRadioButton::indicator:checked {
                border: 1px solid #ffffff;
                border-radius: 7px;
                background-color: #555555;
            }
        """

        volatility_radio.setStyleSheet(radio_style)
        density_radio.setStyleSheet(radio_style)
        fwl_radio.setStyleSheet(radio_style)

        # Set default selection
        volatility_radio.setChecked(True)

        # Add buttons to group
        self.button_group.addButton(volatility_radio, 0)
        self.button_group.addButton(density_radio, 1)
        self.button_group.addButton(fwl_radio, 2)

        # Connect radio buttons to chart switching
        self.button_group.buttonClicked.connect(self.switch_chart)

        # Center the buttons
        header_layout.addStretch()
        header_layout.addWidget(volatility_radio)
        header_layout.addWidget(density_radio)
        header_layout.addWidget(fwl_radio)
        header_layout.addStretch()

        header_widget.setLayout(header_layout)
        return header_widget

    def create_options_header(self):
        """Create options header with bid/ask radio buttons and expiration dropdown."""
        header_widget = QWidget()
        header_widget.setFixedHeight(30)
        header_widget.setStyleSheet("background-color: #2b2b2b;")
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(5, 2, 5, 2)
        header_layout.setSpacing(15)

        # Create price type radio buttons
        self.price_type_group = QButtonGroup()

        bid_radio = QRadioButton("Bid Prices")
        ask_radio = QRadioButton("Ask Prices")

        # Set font size
        font = QFont("Segoe UI", 9)
        bid_radio.setFont(font)
        ask_radio.setFont(font)

        # Style radio buttons to match dark theme
        radio_style = """
            QRadioButton {
                color: #ffffff;
                border: none;
                outline: none;
            }
            QRadioButton::indicator {
                width: 12px;
                height: 12px;
            }
            QRadioButton::indicator:unchecked {
                border: 1px solid #555555;
                border-radius: 6px;
                background-color: #1e1e1e;
            }
            QRadioButton::indicator:checked {
                border: 1px solid #ffffff;
                border-radius: 6px;
                background-color: #555555;
            }
        """

        bid_radio.setStyleSheet(radio_style)
        ask_radio.setStyleSheet(radio_style)

        # Set default to ask prices
        ask_radio.setChecked(True)

        # Add buttons to group
        self.price_type_group.addButton(bid_radio, 0)  # 0 = bid
        self.price_type_group.addButton(ask_radio, 1)  # 1 = ask

        # Connect to update function
        self.price_type_group.buttonClicked.connect(self.on_price_type_changed)

        # Create expiration dropdown
        # Create expiration dropdown (PyQt6 native only, no CSS)
        self.expiry_dropdown = QComboBox()
        self.expiry_dropdown.setFixedWidth(120)
        self.expiry_dropdown.setFont(QFont("Segoe UI", 9))

        # Connect dropdown to update function
        self.expiry_dropdown.currentTextChanged.connect(self.on_expiry_changed)

        # Create label for dropdown
        expiry_label = QLabel("Expiry:")
        expiry_label.setFont(QFont("Segoe UI", 9))
        expiry_label.setStyleSheet("color: #ffffff; border: none;")

        # Layout the header
        header_layout.addStretch()
        header_layout.addWidget(bid_radio)
        header_layout.addWidget(ask_radio)
        header_layout.addWidget(QLabel("|"))  # Separator
        header_layout.addWidget(expiry_label)
        header_layout.addWidget(self.expiry_dropdown)
        header_layout.addStretch()

        header_widget.setLayout(header_layout)
        return header_widget

    def on_price_type_changed(self, button):
        """Handle price type radio button changes."""
        import logging
        logger = logging.getLogger(__name__)

        price_type = 'bid' if self.price_type_group.id(button) == 0 else 'ask'
        logger.info(f"Price type changed to {price_type}")

        # Update density chart
        if hasattr(self, 'density_chart'):
            self.density_chart.set_price_type(price_type)
            # Refresh the chart with current data
            if hasattr(self, '_current_filtered_high_data') and hasattr(self, '_current_filtered_low_data'):
                logger.info(f"Updating density chart with {price_type} prices")
                self.density_chart.update_data(
                    self._current_filtered_high_data,
                    self._current_filtered_low_data,
                    self._current_market_data
                )

        # Update FWL odds chart (check if it has set_price_type method)
        if hasattr(self, 'fwl_odds_chart') and hasattr(self.fwl_odds_chart, 'set_price_type'):
            self.fwl_odds_chart.set_price_type(price_type)
            # Refresh the chart with current data
            if hasattr(self, '_current_filtered_high_data') and hasattr(self, '_current_filtered_low_data'):
                logger.info(f"Updating FWL odds chart with {price_type} prices")
                self.fwl_odds_chart.update_data(
                    self._current_filtered_high_data,
                    self._current_filtered_low_data,
                    self._current_market_data
                )
        else:
            logger.debug("FWL odds chart does not support price type changes")

    def on_expiry_changed(self, expiry_text):
        """Handle expiration dropdown changes."""
        if hasattr(self, 'density_chart') and expiry_text:
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"Expiry changed to {expiry_text}")
            self.density_chart.set_selected_expiry(expiry_text)

            # Refresh the chart with current data and new expiry
            if hasattr(self, '_current_filtered_high_data') and hasattr(self, '_current_filtered_low_data'):
                # Fetch new options data for the selected expiry
                if hasattr(self, '_current_market_data') and self._current_market_data:
                    # Update the density chart with new expiry
                    self.density_chart.update_data(
                        self._current_filtered_high_data,
                        self._current_filtered_low_data,
                        self._current_market_data
                    )

    def create_data_header(self):
        """Create header with 'Projected Data' title."""
        header_widget = QWidget()
        header_widget.setFixedHeight(30)
        header_widget.setStyleSheet("background-color: #2b2b2b;")
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(5, 2, 5, 2)

        # Create title label
        title_label = QLabel("Projected Data")
        title_label.setFont(QFont("Segoe UI", 12))
        title_label.setStyleSheet("color: #ffffff; border: none; outline: none;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Center the title
        header_layout.addStretch()
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        header_widget.setLayout(header_layout)
        return header_widget

    def create_chart_tab(self):
        """Create the Chart tab widget - full width since settings are now unified."""
        chart_widget = QWidget()
        chart_widget.setStyleSheet("border: 2px solid #2b2b2b; background-color: #1e1e1e;")
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Add header with radio buttons
        header = self.create_header_with_radio_buttons()
        layout.addWidget(header)

        # Add options header (only visible when density chart is selected)
        self.options_header = self.create_options_header()
        layout.addWidget(self.options_header)
        self.options_header.setVisible(False)  # Initially hidden

        # Add main content area with stacked widget for charts
        content_area = QWidget()
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(5, 5, 5, 5)
        content_layout.setSpacing(0)

        # Create stacked widget to hold different charts
        self.chart_stack = QStackedWidget()

        # Create and add the three chart types
        self.volatility_chart = VolatilityChart()
        self.density_chart = DensityChart()
        self.fwl_odds_chart = FWLOddsChart()

        self.chart_stack.addWidget(self.volatility_chart)  # Index 0
        self.chart_stack.addWidget(self.density_chart)     # Index 1
        self.chart_stack.addWidget(self.fwl_odds_chart)    # Index 2

        # Set default to volatility chart
        self.chart_stack.setCurrentIndex(0)

        content_layout.addWidget(self.chart_stack)
        content_area.setLayout(content_layout)
        layout.addWidget(content_area)

        chart_widget.setLayout(layout)
        return chart_widget

    def create_data_tab(self):
        """Create the Data tab widget - full width since settings are now unified."""
        data_widget = QWidget()
        data_widget.setStyleSheet("border: 2px solid #2b2b2b; background-color: #1e1e1e;")
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Add header with "Projected Data" title
        header = self.create_data_header()
        layout.addWidget(header)

        # Add main content area with vertical split for high/low data
        content_area = QWidget()
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # Create vertical splitter for high/low data split
        data_splitter = QSplitter(Qt.Orientation.Vertical)
        data_splitter.setChildrenCollapsible(False)
        data_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #2b2b2b;
                height: 2px;
            }
        """)

        # Top section - Projected High Data
        top_widget = QWidget()
        top_widget.setStyleSheet("border: 1px solid #2b2b2b; background-color: #1e1e1e;")
        top_layout = QVBoxLayout()
        top_layout.setContentsMargins(10, 5, 10, 5)
        top_layout.setSpacing(5)

        # Title for high data
        high_title = QLabel("Projected High Data")
        high_title.setFont(QFont("Segoe UI", 12))
        high_title.setStyleSheet("color: #ffffff; font-weight: bold; margin-bottom: 5px;")
        high_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        top_layout.addWidget(high_title)

        # Create high data table
        self.high_table_model = MarketDataTableModel()
        self.high_table_view = QTableView()
        self.high_table_view.setModel(self.high_table_model)
        self.setup_high_table()
        top_layout.addWidget(self.high_table_view)

        top_widget.setLayout(top_layout)

        # Bottom section - Projected Low Data
        bottom_widget = QWidget()
        bottom_widget.setStyleSheet("border: 1px solid #2b2b2b; background-color: #1e1e1e;")
        bottom_layout = QVBoxLayout()
        bottom_layout.setContentsMargins(10, 5, 10, 5)
        bottom_layout.setSpacing(5)

        # Title for low data
        low_title = QLabel("Projected Low Data")
        low_title.setFont(QFont("Segoe UI", 12))
        low_title.setStyleSheet("color: #ffffff; font-weight: bold; margin-bottom: 5px;")
        low_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        bottom_layout.addWidget(low_title)

        # Create low data table
        self.low_table_model = MarketDataTableModel()
        self.low_table_view = QTableView()
        self.low_table_view.setModel(self.low_table_model)
        self.setup_low_table()
        bottom_layout.addWidget(self.low_table_view)

        bottom_widget.setLayout(bottom_layout)

        # Add widgets to splitter
        data_splitter.addWidget(top_widget)
        data_splitter.addWidget(bottom_widget)

        # Set equal sizes for 1/2 split
        data_splitter.setSizes([500, 500])
        data_splitter.setStretchFactor(0, 1)
        data_splitter.setStretchFactor(1, 1)

        # Disable handle movement for fixed split
        handle = data_splitter.handle(1)
        handle.setEnabled(False)

        content_layout.addWidget(data_splitter)
        content_area.setLayout(content_layout)
        layout.addWidget(content_area)

        data_widget.setLayout(layout)
        return data_widget

    def setup_high_table(self):
        """Configure high data table with specific columns."""
        # Set custom headers for high data (Index added as first column)
        # Insert new "Updated" column after Category and "Odds" column after Projected High
        high_headers = [
            "Index", "Weekday", "Category", "Updated",
            "$Change High", "%Change High", "$Projected High Change", "Projected High", "Open", "Close", "Odds"
        ]
        self.high_table_model._headers = high_headers
        self.high_table_model._visible_columns = len(high_headers)

        # Override the data method to insert the "Updated" column (index 3), "Open" column (index 8), "Close" column (index 9), and "Odds" column (index 10)
        original_data_method = self.high_table_model.data
        def custom_high_data(index, role=Qt.ItemDataRole.DisplayRole):
            if role == Qt.ItemDataRole.DisplayRole:
                col = index.column()
                updated_col_index = 3  # "Updated" column position
                open_col_index = 8     # "Open" column position
                close_col_index = 9    # "Close" column position
                odds_col_index = 10    # "Odds" column position

                # Show PH:X in Updated column for high table
                if col == updated_col_index:
                    return self._get_fwl_aggr_display(index.row(), "high")

                # Show open ratio in Open column for high table
                if col == open_col_index:
                    return self._get_open_ratio_display(index.row(), "high")

                # Show close ratio in Close column for high table
                if col == close_col_index:
                    return self._get_close_ratio_display(index.row(), "high")

                # Show odds percentage in Odds column for high table
                if col == odds_col_index:
                    return self._calculate_odds_percentage(index.row(), "high")

                # Map display columns to source row_data indices
                if col < updated_col_index:
                    source_col = col
                elif col < open_col_index:
                    source_col = col - 1  # Account for inserted Updated column
                elif col < close_col_index:
                    source_col = col - 2  # Account for Updated and Open columns
                elif col < odds_col_index:
                    source_col = col - 3  # Account for Updated, Open, and Close columns
                else:
                    source_col = col - 4  # Account for all inserted columns

                row_data = self.high_table_model._data[index.row()] if index.row() < len(self.high_table_model._data) else None
                if row_data and 0 <= source_col < len(row_data):
                    value = row_data[source_col]
                    # Format Projected High (source index 6) to 2 decimals
                    if source_col == 6:
                        try:
                            if value != "" and value is not None:
                                return f"{float(value):.2f}"
                        except (ValueError, TypeError):
                            pass
                    return str(value)
                return ""
            elif role == Qt.ItemDataRole.ForegroundRole:
                # Use same text color as last row
                from PyQt6.QtGui import QColor
                return QColor("#ffffff")
            elif role == Qt.ItemDataRole.BackgroundRole:
                # Use same background color as last row
                from PyQt6.QtGui import QColor
                return QColor("#2b2b2b")
            return original_data_method(index, role)

        self.high_table_model.data = custom_high_data

        # Configure table appearance
        self.setup_table_appearance(self.high_table_view)

    def setup_low_table(self):
        """Configure low data table with specific columns."""
        # Set custom headers for low data (Index added as first column)
        # Insert new "Updated" column after Category and "Odds" column after Projected Low
        low_headers = [
            "Index", "Weekday", "Category", "Updated",
            "$Change Low", "%Change Low", "$Projected Low Change", "Projected Low", "Open", "Close", "Odds"
        ]
        self.low_table_model._headers = low_headers
        self.low_table_model._visible_columns = len(low_headers)

        # Override the data method to insert the "Updated" column (index 3), "Open" column (index 8), "Close" column (index 9), and "Odds" column (index 10)
        original_data_method = self.low_table_model.data
        def custom_low_data(index, role=Qt.ItemDataRole.DisplayRole):
            if role == Qt.ItemDataRole.DisplayRole:
                col = index.column()
                updated_col_index = 3  # "Updated" column position
                open_col_index = 8     # "Open" column position
                close_col_index = 9    # "Close" column position
                odds_col_index = 10    # "Odds" column position

                # Show PL:X in Updated column for low table
                if col == updated_col_index:
                    return self._get_fwl_aggr_display(index.row(), "low")

                # Show open ratio in Open column for low table
                if col == open_col_index:
                    return self._get_open_ratio_display(index.row(), "low")

                # Show close ratio in Close column for low table
                if col == close_col_index:
                    return self._get_close_ratio_display(index.row(), "low")

                # Show odds percentage in Odds column for low table
                if col == odds_col_index:
                    return self._calculate_odds_percentage(index.row(), "low")

                # Map display columns to source row_data indices
                if col < updated_col_index:
                    source_col = col
                elif col < open_col_index:
                    source_col = col - 1  # Account for inserted Updated column
                elif col < close_col_index:
                    source_col = col - 2  # Account for Updated and Open columns
                elif col < odds_col_index:
                    source_col = col - 3  # Account for Updated, Open, and Close columns
                else:
                    source_col = col - 4  # Account for all inserted columns

                row_data = self.low_table_model._data[index.row()] if index.row() < len(self.low_table_model._data) else None
                if row_data and 0 <= source_col < len(row_data):
                    value = row_data[source_col]
                    # Format Projected Low (source index 6) to 2 decimals
                    if source_col == 6:
                        try:
                            if value != "" and value is not None:
                                return f"{float(value):.2f}"
                        except (ValueError, TypeError):
                            pass
                    return str(value)
                return ""
            elif role == Qt.ItemDataRole.ForegroundRole:
                # Use same text color as last row
                from PyQt6.QtGui import QColor
                return QColor("#ffffff")
            elif role == Qt.ItemDataRole.BackgroundRole:
                # Use same background color as last row
                from PyQt6.QtGui import QColor
                return QColor("#2b2b2b")
            return original_data_method(index, role)

        self.low_table_model.data = custom_low_data

        # Configure table appearance
        self.setup_table_appearance(self.low_table_view)

    def setup_table_appearance(self, table_view):
        """Configure table appearance for both high and low tables."""
        # Selection behavior
        table_view.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        table_view.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        # Appearance
        table_view.setAlternatingRowColors(True)
        table_view.setShowGrid(True)
        table_view.setGridStyle(Qt.PenStyle.SolidLine)

        # Header configuration for evenly spread columns
        horizontal_header = table_view.horizontalHeader()
        horizontal_header.setStretchLastSection(True)

        # Set all columns to stretch evenly based on the model attached to this view
        model = table_view.model()
        col_count = model.columnCount() if model is not None else 0
        for i in range(col_count):
            horizontal_header.setSectionResizeMode(i, horizontal_header.ResizeMode.Stretch)

        # Vertical header
        vertical_header = table_view.verticalHeader()
        vertical_header.setVisible(True)
        vertical_header.setDefaultSectionSize(20)  # Smaller rows for split view

        # Dark theme styling
        table_view.setStyleSheet("""
            QTableView {
                background-color: #1e1e1e;
                color: #ffffff;
                gridline-color: #555555;
                selection-background-color: #3c3c3c;
                alternate-background-color: #2b2b2b;
            }
            QHeaderView::section {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 2px;
                font-weight: bold;
                font-size: 9px;
            }
        """)

    def setup_projected_table_view(self):
        """Configure projected table view with same settings as data_tab.py."""
        # Selection behavior
        self.projected_table_view.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.projected_table_view.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        # Appearance
        self.projected_table_view.setAlternatingRowColors(True)
        self.projected_table_view.setShowGrid(True)
        self.projected_table_view.setGridStyle(Qt.PenStyle.SolidLine)

        # Header configuration for evenly spread columns
        horizontal_header = self.projected_table_view.horizontalHeader()
        horizontal_header.setStretchLastSection(True)

        # Set all columns to stretch evenly
        for i in range(self.projected_table_model.columnCount()):
            horizontal_header.setSectionResizeMode(i, horizontal_header.ResizeMode.Stretch)

        # Vertical header
        vertical_header = self.projected_table_view.verticalHeader()
        vertical_header.setVisible(True)
        vertical_header.setDefaultSectionSize(25)

        # Dark theme styling to match the application
        self.projected_table_view.setStyleSheet("""
            QTableView {
                background-color: #1e1e1e;
                color: #ffffff;
                gridline-color: #555555;
                selection-background-color: #3c3c3c;
                alternate-background-color: #2b2b2b;
            }
            QHeaderView::section {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 4px;
                font-weight: bold;
            }
        """)

    def _calculate_odds_percentage(self, table_row_index, table_type):
        """Calculate FWL odds percentage for a specific row using the same method as FWL Odds chart.

        Args:
            table_row_index: The row index in the volatility table
            table_type: "high" or "low" to determine which data to use
        """
        try:
            # Get all projected values for the current table type
            if table_type == "high":
                table_model = self.high_table_model
                projected_col_index = 6  # Projected High is at source index 6
            else:  # table_type == "low"
                table_model = self.low_table_model
                projected_col_index = 6  # Projected Low is at source index 6

            # Extract all projected values from the table data
            projected_values = []
            for row_data in table_model._data:
                if len(row_data) > projected_col_index:
                    try:
                        projected_str = str(row_data[projected_col_index])
                        if projected_str and projected_str != '' and projected_str != 'None':
                            projected_value = float(projected_str)
                            projected_values.append(projected_value)
                    except (ValueError, TypeError):
                        continue

            if not projected_values or table_row_index >= len(table_model._data):
                return ""

            # Get the current row's projected value
            current_row_data = table_model._data[table_row_index]
            if len(current_row_data) <= projected_col_index:
                return ""

            try:
                current_projected_str = str(current_row_data[projected_col_index])
                if not current_projected_str or current_projected_str == '' or current_projected_str == 'None':
                    return ""
                current_projected = float(current_projected_str)
            except (ValueError, TypeError):
                return ""

            # Calculate odds using the same method as FWL Odds chart
            sorted_values = sorted(projected_values)
            n_values = len(sorted_values)

            if n_values == 1:
                percentage = 25.5  # Single value gets midpoint
            else:
                # Find the rank/position of this value in the sorted list
                value_rank = sorted_values.index(current_projected)

                if table_type == "high":
                    # For highs: highest (last in sorted list) = 1%, lowest (first in sorted list) = 50%
                    # Reverse the rank so highest value gets rank 0
                    reversed_rank = n_values - 1 - value_rank
                    ratio = reversed_rank / (n_values - 1)
                    percentage = 1.0 + (ratio * 49.0)  # 1% to 50%
                else:  # table_type == "low"
                    # For lows: lowest (first in sorted list) = 1%, highest (last in sorted list) = 50%
                    ratio = value_rank / (n_values - 1)
                    percentage = 1.0 + (ratio * 49.0)  # 1% to 50%

            return f"{percentage:.1f}%"

        except Exception as e:
            return ""

    def _get_fwl_aggr_display(self, table_row_index, table_type="both"):
        """Get FWL Aggr display text for Updated column.

        Args:
            table_row_index: The row index in the volatility table
            table_type: "high", "low", or "both" to determine what to display
        """
        try:
            # Get FWL Aggr value from stored market data
            if hasattr(self, '_market_data') and self._market_data:
                fwl_aggr = self._market_data.get('fwl_aggr_value', 1)

                # Get the original data index from the volatility table data
                # The first column (index 0) contains the original row index from main data table
                original_data_index = None
                if table_type == "high" and hasattr(self, 'high_table_model'):
                    if table_row_index < len(self.high_table_model._data):
                        row_data = self.high_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index
                elif table_type == "low" and hasattr(self, 'low_table_model'):
                    if table_row_index < len(self.low_table_model._data):
                        row_data = self.low_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index

                if original_data_index is not None:
                    # Get specific PH/PL values for this original data index from metadata
                    fwl_metadata = self._market_data.get('fwl_aggr_metadata', {})
                    ph = fwl_aggr  # Default value
                    pl = fwl_aggr  # Default value

                    if str(original_data_index) in fwl_metadata:
                        metadata_row = fwl_metadata[str(original_data_index)]
                        ph = metadata_row.get('ph', fwl_aggr)
                        pl = metadata_row.get('pl', fwl_aggr)
                    elif original_data_index in fwl_metadata:
                        metadata_row = fwl_metadata[original_data_index]
                        ph = metadata_row.get('ph', fwl_aggr)
                        pl = metadata_row.get('pl', fwl_aggr)

                    # Return based on table type
                    if table_type == "high":
                        return f"PH:{ph}"
                    elif table_type == "low":
                        return f"PL:{pl}"
                    else:  # both
                        return f"PH:{ph}|PL:{pl}"
            return ""
        except Exception as e:
            print(f"Error getting FWL Aggr display: {e}")
            return ""

    def _get_open_ratio_display(self, table_row_index, table_type="both"):
        """Get open ratio display for Open column.

        Args:
            table_row_index: The row index in the volatility table
            table_type: "high" or "low" to determine which table to use
        """
        try:
            # Get the projected OHLC data from market data
            if hasattr(self, '_market_data') and self._market_data:
                projected_ohlc_rows = self._market_data.get('projected_ohlc_table_rows', [])

                # Get the original data index from the volatility table data
                original_data_index = None
                if table_type == "high" and hasattr(self, 'high_table_model'):
                    if table_row_index < len(self.high_table_model._data):
                        row_data = self.high_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index
                elif table_type == "low" and hasattr(self, 'low_table_model'):
                    if table_row_index < len(self.low_table_model._data):
                        row_data = self.low_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index

                # Get open ratio from projected OHLC data (column index 11)
                if original_data_index is not None and projected_ohlc_rows:
                    try:
                        original_index = int(original_data_index)
                        if 0 <= original_index < len(projected_ohlc_rows):
                            projected_row = projected_ohlc_rows[original_index]
                            if len(projected_row) > 11:  # Open ratio is at index 11
                                open_ratio = projected_row[11]
                                if open_ratio and open_ratio != "":
                                    return str(open_ratio)
                    except (ValueError, IndexError):
                        pass
            return ""
        except Exception as e:
            print(f"Error getting open ratio display: {e}")
            return ""

    def _get_close_ratio_display(self, table_row_index, table_type="both"):
        """Get close ratio display for Close column.

        Args:
            table_row_index: The row index in the volatility table
            table_type: "high" or "low" to determine which table to use
        """
        try:
            # Get the projected OHLC data from market data
            if hasattr(self, '_market_data') and self._market_data:
                projected_ohlc_rows = self._market_data.get('projected_ohlc_table_rows', [])

                # Get the original data index from the volatility table data
                original_data_index = None
                if table_type == "high" and hasattr(self, 'high_table_model'):
                    if table_row_index < len(self.high_table_model._data):
                        row_data = self.high_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index
                elif table_type == "low" and hasattr(self, 'low_table_model'):
                    if table_row_index < len(self.low_table_model._data):
                        row_data = self.low_table_model._data[table_row_index]
                        if row_data and len(row_data) > 0:
                            original_data_index = row_data[0]  # First column is the original index

                # Get close ratio from projected OHLC data (column index 12)
                # The projected OHLC structure: Date, Weekday, Category, $Change High, $Change Low,
                # %Change High, %Change Low, $Projected High Change, $Projected Low Change,
                # Projected High, Projected Low, Open ratio, Close ratio, bg_color, fg_color
                if original_data_index is not None and projected_ohlc_rows:
                    try:
                        original_index = int(original_data_index)
                        if 0 <= original_index < len(projected_ohlc_rows):
                            projected_row = projected_ohlc_rows[original_index]
                            if len(projected_row) > 12:  # Close ratio is at index 12
                                close_ratio = projected_row[12]
                                if close_ratio and close_ratio != "":
                                    return str(close_ratio)
                    except (ValueError, IndexError):
                        pass
            return ""
        except Exception as e:
            print(f"Error getting close ratio display: {e}")
            return ""

    def update_data(self, market_data: Dict[str, Any]):
        """Update both high and low data tables with pre-computed data from backend."""
        if not hasattr(self, 'high_table_model') or not hasattr(self, 'low_table_model'):
            return

        # Get pre-computed volatility statistics data from backend
        high_data = market_data.get("volatility_high_data", [])
        low_data = market_data.get("volatility_low_data", [])
        filtered_high_data = market_data.get("volatility_filtered_high_data", [])
        filtered_low_data = market_data.get("volatility_filtered_low_data", [])

        if not high_data or not low_data:
            return

        # Store original data for unified filtering across all instances
        self._original_high_data = high_data
        self._original_low_data = low_data
        self._market_data = market_data  # Store market data for chart updates
        self._current_market_data = market_data  # Store for options callbacks

        # Update expiry dropdown if density chart is available
        self.update_expiry_dropdown(market_data)

        # Update tables with filtered data (initially H/L matching filtered)
        self.high_table_model.beginResetModel()
        self.high_table_model._data = filtered_high_data
        self.high_table_model.endResetModel()

        self.low_table_model.beginResetModel()
        self.low_table_model._data = filtered_low_data
        self.low_table_model.endResetModel()

        # Update charts with filtered data and market data for close price
        self.update_charts_with_filtered_data(filtered_high_data, filtered_low_data, market_data)

        # Update bias statistics (always shown regardless of mode)
        self.update_bias_statistics_standalone()

        # Apply unified filter to ensure consistency across all tabs
        self.apply_unified_filter()

    def update_bias_statistics_standalone(self):
        """Update bias statistics independently - called from main update_data method."""
        try:
            # Check if we have market data
            if not hasattr(self, '_market_data') or not self._market_data:
                return

            market_data = self._market_data
            projected_ohlc_rows = market_data.get('projected_ohlc_table_rows', [])

            if not projected_ohlc_rows:
                return

            # Calculate weekday bias statistics
            weekday_bias = self.density_chart._calculate_weekday_bias(projected_ohlc_rows)

            # Calculate H/L matching bias statistics
            hl_bias = self.density_chart._calculate_hl_matching_bias(projected_ohlc_rows, market_data)

            # Find the combined statistics layout
            if hasattr(self.density_chart, 'combined_statistics_layout'):
                # Clear existing bias statistics (but not all statistics)
                self.density_chart._clear_bias_statistics()

                # Add bias statistics title
                bias_stats_title = QLabel("Bias")
                bias_stats_title.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
                bias_stats_title.setStyleSheet("color: #ffffff; margin-bottom: 5px; padding: 2px;")
                bias_stats_title.setObjectName("bias_title")  # For identification
                self.density_chart.combined_statistics_layout.insertWidget(0, bias_stats_title)  # Insert at top

                # Add bias column headers
                self.density_chart._add_bias_headers()

                # Add weekday bias statistics
                if weekday_bias:
                    for label, data in weekday_bias.items():
                        if isinstance(data, dict):
                            self.density_chart._add_bias_row_standalone(label, data["percentage"], data["count"])
                        else:
                            # Fallback for old format
                            self.density_chart._add_bias_row_standalone(label, data, "")

                # Add H/L matching bias statistics
                if hl_bias:
                    for label, data in hl_bias.items():
                        if isinstance(data, dict):
                            self.density_chart._add_bias_row_standalone(label, data["percentage"], data["count"])
                        else:
                            # Fallback for old format
                            self.density_chart._add_bias_row_standalone(label, data, "")

        except Exception as e:
            print(f"Error updating standalone bias statistics: {e}")

    def update_expiry_dropdown(self, market_data):
        """Update the expiry dropdown with available expiration dates."""
        try:
            if not hasattr(self, 'expiry_dropdown') or not hasattr(self, 'density_chart'):
                return

            # Fetch options data to get available expiration dates
            ticker = market_data.get("ticker", "") if market_data else ""
            options_data = self.data_service.get_options_data(ticker) if ticker and self.data_service else None

            if options_data and 'expiry_dates' in options_data:
                expiry_dates = options_data['expiry_dates']

                # Clear and populate dropdown
                self.expiry_dropdown.clear()
                if expiry_dates:
                    self.expiry_dropdown.addItems(expiry_dates)

                    # Set default expiry if available
                    default_expiry = options_data.get('default_expiry')
                    if default_expiry and default_expiry in expiry_dates:
                        self.expiry_dropdown.setCurrentText(default_expiry)
                        self.density_chart.set_selected_expiry(default_expiry)
                    elif expiry_dates:
                        # Set first expiry as default
                        self.expiry_dropdown.setCurrentText(expiry_dates[0])
                        self.density_chart.set_selected_expiry(expiry_dates[0])

        except Exception as e:
            print(f"Error updating expiry dropdown: {e}")

    def update_charts_with_filtered_data(self, filtered_high_data, filtered_low_data, market_data=None):
        """Update all chart widgets with filtered data from backend."""
        try:
            # Store current filtered data for options callbacks
            self._current_filtered_high_data = filtered_high_data
            self._current_filtered_low_data = filtered_low_data

            # Update volatility chart with market data for close price
            if hasattr(self, 'volatility_chart'):
                self.volatility_chart.update_data(filtered_high_data, filtered_low_data, market_data)

            # Update density chart with market data
            if hasattr(self, 'density_chart'):
                self.density_chart.update_data(filtered_high_data, filtered_low_data, market_data)

            # Update FWL odds chart with market data for last close price
            if hasattr(self, 'fwl_odds_chart'):
                self.fwl_odds_chart.update_data(filtered_high_data, filtered_low_data, market_data)

        except Exception as e:
            print(f"Error updating charts with filtered data: {e}")

    def _increment_fwl_aggr(self):
        """Increment FWL Aggr. value with bounds checking."""
        try:
            current_value = int(self.fwl_aggr_input.text())
            if current_value < 10:
                self.fwl_aggr_input.setText(str(current_value + 1))
        except ValueError:
            self.fwl_aggr_input.setText("1")  # Reset to default if invalid

    def _decrement_fwl_aggr(self):
        """Decrement FWL Aggr. value with bounds checking."""
        try:
            current_value = int(self.fwl_aggr_input.text())
            if current_value > 1:
                self.fwl_aggr_input.setText(str(current_value - 1))
        except ValueError:
            self.fwl_aggr_input.setText("1")  # Reset to default if invalid

    def _validate_fwl_aggr_input(self):
        """Validate FWL Aggr. input and enforce bounds."""
        try:
            text = self.fwl_aggr_input.text()
            if text:  # Only validate if there's text
                value = int(text)
                if value < 1:
                    self.fwl_aggr_input.setText("1")
                elif value > 10:
                    self.fwl_aggr_input.setText("10")
        except ValueError:
            # Don't immediately reset on invalid input to allow typing
            pass

    def get_fwl_aggr_value(self):
        """Get the current FWL Aggr. value as an integer."""
        try:
            return int(self.fwl_aggr_input.text())
        except ValueError:
            return 1  # Default value

    def _on_fwl_aggr_changed(self):
        """Handle FWL Aggr value changes and trigger debounced data refresh."""
        try:
            # Only trigger refresh if the value is valid
            value = self.get_fwl_aggr_value()
            if 1 <= value <= 10:
                # Use debounce timer to prevent too many rapid refreshes
                self.fwl_aggr_timer.stop()  # Stop any existing timer
                self.fwl_aggr_timer.start(500)  # Wait 500ms before triggering refresh

        except Exception as e:
            print(f"Error handling FWL Aggr change: {e}")

    def _trigger_data_refresh(self):
        """Actually trigger the data refresh after debounce delay."""
        try:
            value = self.get_fwl_aggr_value()
            print(f"[FWL AGGR] Triggering data refresh with FWL Aggr value: {value}")
            if hasattr(self, '_main_window_ref') and self._main_window_ref:
                main_window = self._main_window_ref
                if hasattr(main_window, 'universal_controls'):
                    # Trigger data refresh with new FWL Aggr value
                    main_window.universal_controls.refresh_current_data()
                    print(f"Data refresh triggered with FWL Aggr: {value}")
        except Exception as e:
            print(f"Error triggering data refresh: {e}")

    def set_main_window_ref(self, main_window):
        """Set reference to main window for triggering data refresh."""
        self._main_window_ref = main_window

    def get_occurrence_count(self):
        """Get the current occurrence count value.

        Returns:
            int: The current occurrence count value, or 0 if invalid (0 means all occurrences)
        """
        try:
            return int(self.occurrence_input.text())
        except (ValueError, AttributeError):
            return 0

    def _on_occurrence_changed(self):
        """Handle occurrence limiter value changes and trigger data refresh."""
        try:
            value = int(self.occurrence_input.text())
            if value >= 0:
                print(f"[OCCURRENCE LIMITER] Value changed to: {value}")
                # Apply the occurrence limit to current data
                self._apply_occurrence_limit()
        except ValueError:
            pass  # Invalid input, ignore

    def _validate_occurrence_input(self):
        """Validate occurrence input and apply styling."""
        try:
            value = int(self.occurrence_input.text())
            if value >= 0:
                # Valid input - normal styling
                self.occurrence_input.setStyleSheet("""
                    QLineEdit {
                        background-color: #2b2b2b;
                        color: #ffffff;
                        border: 1px solid #555555;
                        border-radius: 3px;
                        padding: 2px;
                    }
                """)
            else:
                # Invalid input - red border
                self.occurrence_input.setStyleSheet("""
                    QLineEdit {
                        background-color: #2b2b2b;
                        color: #ffffff;
                        border: 2px solid #ff4444;
                        border-radius: 3px;
                        padding: 2px;
                    }
                """)
        except ValueError:
            # Invalid input - red border
            self.occurrence_input.setStyleSheet("""
                QLineEdit {
                    background-color: #2b2b2b;
                    color: #ffffff;
                    border: 2px solid #ff4444;
                    border-radius: 3px;
                    padding: 2px;
                }
            """)

    def _increment_occurrence(self):
        """Increment occurrence value by 1."""
        try:
            current_value = int(self.occurrence_input.text())
            new_value = current_value + 1  # No max limit
            self.occurrence_input.setText(str(new_value))
        except ValueError:
            self.occurrence_input.setText("0")

    def _decrement_occurrence(self):
        """Decrement occurrence value by 1."""
        try:
            current_value = int(self.occurrence_input.text())
            new_value = max(current_value - 1, 0)  # Min value of 0
            self.occurrence_input.setText(str(new_value))
        except ValueError:
            self.occurrence_input.setText("0")

    def _apply_occurrence_limit(self):
        """Apply the occurrence limit to the current data."""
        try:
            occurrence_count = self.get_occurrence_count()

            if occurrence_count == 0:
                # Use all data - revert to original data
                if hasattr(self, '_original_high_data') and hasattr(self, '_original_low_data'):
                    # Apply current filter to original data
                    filter_result = self.data_service.apply_volatility_statistics_filter(
                        self._original_high_data, self._original_low_data, self._unified_filter_type
                    )
                    filtered_high = filter_result.get("volatility_filtered_high_data", [])
                    filtered_low = filter_result.get("volatility_filtered_low_data", [])

                    # Update tables
                    self._update_tables_with_data(filtered_high, filtered_low)
            else:
                # Limit to the latest N occurrences
                if hasattr(self, '_original_high_data') and hasattr(self, '_original_low_data'):
                    # Apply filter first
                    filter_result = self.data_service.apply_volatility_statistics_filter(
                        self._original_high_data, self._original_low_data, self._unified_filter_type
                    )
                    filtered_high = filter_result.get("volatility_filtered_high_data", [])
                    filtered_low = filter_result.get("volatility_filtered_low_data", [])

                    # Limit to latest N occurrences (chronologically)
                    # The data is already sorted chronologically, so take the last N items
                    if len(filtered_high) > occurrence_count:
                        filtered_high = filtered_high[-occurrence_count:]
                    if len(filtered_low) > occurrence_count:
                        filtered_low = filtered_low[-occurrence_count:]

                    # Update tables
                    self._update_tables_with_data(filtered_high, filtered_low)

        except Exception as e:
            print(f"Error applying occurrence limit: {e}")

    def _update_tables_with_data(self, filtered_high, filtered_low):
        """Update both tables with the provided data."""
        try:
            # Update high table
            if hasattr(self, 'high_table_model'):
                self.high_table_model.beginResetModel()
                self.high_table_model._data = filtered_high
                self.high_table_model.endResetModel()

            # Update low table
            if hasattr(self, 'low_table_model'):
                self.low_table_model.beginResetModel()
                self.low_table_model._data = filtered_low
                self.low_table_model.endResetModel()

            # Update charts if they exist
            if hasattr(self, '_market_data'):
                self.update_charts_with_filtered_data(filtered_high, filtered_low, self._market_data)

        except Exception as e:
            print(f"Error updating tables with data: {e}")

    def _on_zones_viewer_button_clicked(self):
        """Handle zones viewer button click - opens text viewer for zone prices."""
        try:
            # Create and show the zones text viewer dialog
            dialog = ZonesTextViewerDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"Error opening zones viewer: {e}")


class ZonesTextViewerDialog(QDialog):
    """Dialog for viewing zone prices in a text viewer."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Zone Prices Viewer")
        self.setModal(True)
        self.resize(600, 400)

        # Set dark theme styling
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10pt;
                padding: 8px;
            }
            QPushButton {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 6px 12px;
                border-radius: 3px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #3c3c3c;
            }
            QPushButton:pressed {
                background-color: #1e1e1e;
            }
        """)

        self.setup_ui()
        self.load_zone_data()

    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Title label
        title_label = QLabel("Zone Prices")
        title_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #ffffff; margin-bottom: 5px;")
        layout.addWidget(title_label)

        # Text viewer
        self.text_viewer = QTextEdit()
        self.text_viewer.setReadOnly(True)
        layout.addWidget(self.text_viewer)

        # Button layout
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def load_zone_data(self):
        """Load and display zone price data."""
        # Empty for now - will be used as a cache for zone data
        self.text_viewer.setPlainText("")

    def get_shared_options_data(self, market_data, selected_expiry=None):
        """Get options data shared across all charts in this tab instance."""
        try:
            import time

            ticker = market_data.get("ticker", "")
            if not ticker:
                return None

            # Determine expiry
            if selected_expiry is None:
                selected_expiry = getattr(self.density_chart, 'selected_expiry', None)
                if not selected_expiry:
                    # Get from any chart that has it
                    for chart in [self.density_chart, self.fwl_odds_chart]:
                        if hasattr(chart, 'selected_expiry') and chart.selected_expiry:
                            selected_expiry = chart.selected_expiry
                            break

                    if not selected_expiry:
                        # Fetch available dates and use first one
                        expiry_dates = self.density_chart._fetch_available_expiry_dates(ticker)
                        if expiry_dates:
                            selected_expiry = expiry_dates[0]
                        else:
                            return None

            # Check instance-level cache first (5 minute TTL)
            current_time = time.time()
            cache_key = (ticker, selected_expiry)

            if (self._shared_options_data and
                hasattr(self, '_shared_cache_key') and
                self._shared_cache_key == cache_key and
                current_time - self._shared_options_timestamp < 300):
                import logging
                logger = logging.getLogger(__name__)
                logger.debug(f"Using shared instance cache for {ticker} expiry {selected_expiry}")
                return self._shared_options_data

            # Fetch fresh data using the backend options service
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"Fetching shared options data for all charts: {ticker} expiry {selected_expiry}")
            options_data = self.data_service.get_options_data(ticker, selected_expiry) if self.data_service else None

            if options_data:
                # Cache at instance level
                self._shared_options_data = options_data
                self._shared_options_timestamp = current_time
                self._shared_cache_key = cache_key
                logger.debug(f"Cached shared options data for {ticker} expiry {selected_expiry}")

                # Also ensure all charts have the same selected expiry
                for chart in [self.density_chart, self.fwl_odds_chart]:
                    if hasattr(chart, 'selected_expiry'):
                        chart.selected_expiry = selected_expiry

            return options_data

        except Exception as e:
            print(f"Error getting shared options data: {e}")
            return None
