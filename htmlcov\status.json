{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.4", "globals": "6707ab23c58cf67381baef416598ec5b", "files": {"z_798a2b81d00ca5b7___init___py": {"hash": "5db5a1e7e387618ebe62e2daebb24b22", "index": {"url": "z_798a2b81d00ca5b7___init___py.html", "file": "src\\backend\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_buffer_utils_py": {"hash": "4a9731cb6b0c8f58b882b56a951f7ea2", "index": {"url": "z_798a2b81d00ca5b7_buffer_utils_py.html", "file": "src\\backend\\buffer_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 5, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_chart_data_models_py": {"hash": "9795408aaecd2c6337e0d9a2bf9c2741", "index": {"url": "z_798a2b81d00ca5b7_chart_data_models_py.html", "file": "src\\backend\\chart_data_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 114, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_computation_offloader_py": {"hash": "9ad5e8251b4dae496b179cb9501507e0", "index": {"url": "z_798a2b81d00ca5b7_computation_offloader_py.html", "file": "src\\backend\\computation_offloader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 213, "n_excluded": 0, "n_missing": 213, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_data_service_py": {"hash": "763a2a00e8ccdc775213c127a4747cd0", "index": {"url": "z_798a2b81d00ca5b7_data_service_py.html", "file": "src\\backend\\data_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 855, "n_excluded": 0, "n_missing": 783, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_density_calculations_service_py": {"hash": "3ab50e7c295a927fb531bc0df7e6b4e3", "index": {"url": "z_798a2b81d00ca5b7_density_calculations_service_py.html", "file": "src\\backend\\density_calculations_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 453, "n_excluded": 0, "n_missing": 429, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_metrics_server_py": {"hash": "6ca104ea79644a80749b6585f7308d29", "index": {"url": "z_798a2b81d00ca5b7_metrics_server_py.html", "file": "src\\backend\\metrics_server.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 183, "n_excluded": 0, "n_missing": 183, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_models_py": {"hash": "992263feb447057556172a60bc007576", "index": {"url": "z_798a2b81d00ca5b7_models_py.html", "file": "src\\backend\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 186, "n_excluded": 0, "n_missing": 186, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_observability_py": {"hash": "7636e4080744b51d7c54c231b66924b1", "index": {"url": "z_798a2b81d00ca5b7_observability_py.html", "file": "src\\backend\\observability.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 175, "n_excluded": 0, "n_missing": 81, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_options_service_py": {"hash": "ae725d49cb6836b529aad704c92a1a7c", "index": {"url": "z_798a2b81d00ca5b7_options_service_py.html", "file": "src\\backend\\options_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 295, "n_excluded": 0, "n_missing": 277, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_rebasing_py": {"hash": "1aaf02fbc38745ed7daca632e0183de9", "index": {"url": "z_798a2b81d00ca5b7_rebasing_py.html", "file": "src\\backend\\rebasing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 110, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_resource_manager_py": {"hash": "9c619657f365b6cb01c2be884e5aaa9f", "index": {"url": "z_798a2b81d00ca5b7_resource_manager_py.html", "file": "src\\backend\\resource_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 176, "n_excluded": 0, "n_missing": 176, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_shared_memory_manager_py": {"hash": "14a9ea9def86f4d44cfac7c590cb435a", "index": {"url": "z_798a2b81d00ca5b7_shared_memory_manager_py.html", "file": "src\\backend\\shared_memory_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 168, "n_excluded": 0, "n_missing": 104, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_signals_py": {"hash": "b249b02cf53fe35fa1e010a2d86b9a59", "index": {"url": "z_798a2b81d00ca5b7_signals_py.html", "file": "src\\backend\\signals.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_visualization_service_py": {"hash": "31d6939755254d2bc05afa2d5a6bacf5", "index": {"url": "z_798a2b81d00ca5b7_visualization_service_py.html", "file": "src\\backend\\visualization_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 160, "n_excluded": 0, "n_missing": 160, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_visualization_worker_py": {"hash": "8481b57f41460d4a41d55e5797fd2271", "index": {"url": "z_798a2b81d00ca5b7_visualization_worker_py.html", "file": "src\\backend\\visualization_worker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 134, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_798a2b81d00ca5b7_volatility_calculations_service_py": {"hash": "db8117ff82a694161253ffb9769b7218", "index": {"url": "z_798a2b81d00ca5b7_volatility_calculations_service_py.html", "file": "src\\backend\\volatility_calculations_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 123, "n_excluded": 0, "n_missing": 107, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}