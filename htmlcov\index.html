<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">9%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.4">coverage.py v7.10.4</a>,
            created at 2025-08-25 22:53 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7___init___py.html">src\backend\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html">src\backend\buffer_utils.py</a></td>
                <td>106</td>
                <td>74</td>
                <td>5</td>
                <td class="right" data-ratio="32 106">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html">src\backend\chart_data_models.py</a></td>
                <td>114</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="0 114">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html">src\backend\computation_offloader.py</a></td>
                <td>213</td>
                <td>213</td>
                <td>0</td>
                <td class="right" data-ratio="0 213">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html">src\backend\data_service.py</a></td>
                <td>855</td>
                <td>783</td>
                <td>0</td>
                <td class="right" data-ratio="72 855">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html">src\backend\density_calculations_service.py</a></td>
                <td>453</td>
                <td>429</td>
                <td>0</td>
                <td class="right" data-ratio="24 453">5%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html">src\backend\metrics_server.py</a></td>
                <td>183</td>
                <td>183</td>
                <td>0</td>
                <td class="right" data-ratio="0 183">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html">src\backend\models.py</a></td>
                <td>186</td>
                <td>186</td>
                <td>0</td>
                <td class="right" data-ratio="0 186">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html">src\backend\observability.py</a></td>
                <td>175</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="94 175">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html">src\backend\options_service.py</a></td>
                <td>295</td>
                <td>277</td>
                <td>0</td>
                <td class="right" data-ratio="18 295">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_rebasing_py.html">src\backend\rebasing.py</a></td>
                <td>110</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html">src\backend\resource_manager.py</a></td>
                <td>176</td>
                <td>176</td>
                <td>0</td>
                <td class="right" data-ratio="0 176">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html">src\backend\shared_memory_manager.py</a></td>
                <td>168</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="64 168">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html">src\backend\signals.py</a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html">src\backend\visualization_service.py</a></td>
                <td>160</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="0 160">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_worker_py.html">src\backend\visualization_worker.py</a></td>
                <td>134</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="0 134">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html">src\backend\volatility_calculations_service.py</a></td>
                <td>123</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="16 123">13%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>3551</td>
                <td>3231</td>
                <td>5</td>
                <td class="right" data-ratio="320 3551">9%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.4">coverage.py v7.10.4</a>,
            created at 2025-08-25 22:53 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_798a2b81d00ca5b7___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
