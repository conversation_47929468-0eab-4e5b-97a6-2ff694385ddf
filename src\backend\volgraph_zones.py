"""
Volgraph Zones Service - Backend calculation of volatility zones.

This module handles the calculation of volatility zones for the volgraph system.
All zone calculations are performed in the backend to maintain separation of concerns
and ensure consistent, high-performance computations.
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from .observability import get_structured_logger, trace_operation


@dataclass
class ZoneData:
    """Data structure for a volatility zone."""
    price: float
    zone_type: str  # 'high' or 'low'
    strength: int
    count: int
    winrate: float
    level_name: str


@dataclass
class ZoneCalculationResult:
    """Result of zone calculations."""
    zones: List[ZoneData]
    high_zones: List[ZoneData]
    low_zones: List[ZoneData]
    calculation_metadata: Dict[str, Any]


class VolgraphZonesService:
    """
    Service for calculating volatility zones in the backend.
    
    This service handles all zone-related calculations including:
    - Zone identification and classification
    - Strength calculations
    - Winrate analysis
    - Zone filtering and ranking
    """
    
    def __init__(self):
        self.logger = get_structured_logger("VolgraphZonesService")
        self._zone_cache: Dict[str, ZoneCalculationResult] = {}
        
    def calculate_zones(
        self,
        high_data: List[Any],
        low_data: List[Any],
        market_data: Optional[Dict[str, Any]] = None,
        calculation_params: Optional[Dict[str, Any]] = None
    ) -> ZoneCalculationResult:
        """
        Calculate volatility zones from high and low data - ALL CALCULATIONS IN BACKEND.

        Args:
            high_data: Raw high volatility data from data service
            low_data: Raw low volatility data from data service
            market_data: Optional market data context
            calculation_params: Optional parameters for zone calculations

        Returns:
            ZoneCalculationResult containing all calculated zones
        """
        with trace_operation("calculate_zones"):
            try:
                self.logger.info("Starting complete backend zone calculations")

                # Step 1: Calculate H/L matching zones (ALL IN BACKEND)
                hl_matching_stats = self._calculate_hl_matching_statistics(high_data, low_data)

                # Step 2: Calculate Weekday matching zones (ALL IN BACKEND)
                weekday_matching_stats = self._calculate_weekday_matching_statistics(high_data, low_data)

                # Step 3: Apply filtering to select optimal zones
                filtered_high_zones = self._apply_zone_filtering(hl_matching_stats, weekday_matching_stats, 'high')
                filtered_low_zones = self._apply_zone_filtering(hl_matching_stats, weekday_matching_stats, 'low')

                # Step 4: Extend filtered zones by +-0.033%
                extended_high_zones = self._extend_zones_by_percentage(filtered_high_zones, 'high')
                extended_low_zones = self._extend_zones_by_percentage(filtered_low_zones, 'low')

                # Step 5: Create zone objects from extended filtered statistics
                high_zones = self._create_zones_from_extended_data(extended_high_zones, 'high')
                low_zones = self._create_zones_from_extended_data(extended_low_zones, 'low')

                # Combine all zones
                all_zones = high_zones + low_zones

                # Create calculation metadata
                metadata = {
                    "total_zones": len(all_zones),
                    "high_zones_count": len(high_zones),
                    "low_zones_count": len(low_zones),
                    "calculation_timestamp": self._get_timestamp(),
                    "hl_matching_stats": hl_matching_stats,
                    "weekday_matching_stats": weekday_matching_stats,
                    "filtered_high_zones": filtered_high_zones,
                    "filtered_low_zones": filtered_low_zones,
                    "extended_high_zones": extended_high_zones,
                    "extended_low_zones": extended_low_zones,
                    "filtering_applied": True,
                    "extension_applied": True,
                    "extension_percentage": 0.033
                }

                result = ZoneCalculationResult(
                    zones=all_zones,
                    high_zones=high_zones,
                    low_zones=low_zones,
                    calculation_metadata=metadata
                )

                self.logger.info(f"Backend zone calculations completed: {len(all_zones)} total zones")
                return result

            except Exception as e:
                self.logger.error(f"Error in backend zone calculations: {e}")
                # Return empty result on error
                return ZoneCalculationResult(
                    zones=[],
                    high_zones=[],
                    low_zones=[],
                    calculation_metadata={"error": str(e)}
                )
    
    def _calculate_hl_matching_statistics(self, high_data: List[Any], low_data: List[Any]) -> Dict[str, Any]:
        """Calculate H/L matching statistics - ALL IN BACKEND."""
        try:
            # Apply H/L matching filter (filter_type = 0) using vectorized operations
            filtered_high, filtered_low = self._apply_hl_matching_filter(high_data, low_data)

            # Calculate statistics for highs and lows using vectorized operations
            highs_stats = self._calculate_statistics_vectorized(filtered_high, 'high')
            lows_stats = self._calculate_statistics_vectorized(filtered_low, 'low')

            result = {
                'highs': highs_stats,
                'lows': lows_stats,
                'filter_type': 'H/L_matching'
            }

            self.logger.debug(f"H/L matching stats calculated: {len(filtered_high)} highs, {len(filtered_low)} lows")
            return result

        except Exception as e:
            self.logger.error(f"Error calculating H/L matching statistics: {e}")
            return {'highs': {}, 'lows': {}, 'filter_type': 'H/L_matching'}

    def _calculate_weekday_matching_statistics(self, high_data: List[Any], low_data: List[Any]) -> Dict[str, Any]:
        """Calculate Weekday matching statistics - ALL IN BACKEND."""
        try:
            # Apply Weekday matching filter (filter_type = 1) using vectorized operations
            filtered_high, filtered_low = self._apply_weekday_matching_filter(high_data, low_data)

            # Calculate statistics for highs and lows using vectorized operations
            highs_stats = self._calculate_statistics_vectorized(filtered_high, 'high')
            lows_stats = self._calculate_statistics_vectorized(filtered_low, 'low')

            result = {
                'highs': highs_stats,
                'lows': lows_stats,
                'filter_type': 'weekday_matching'
            }

            self.logger.debug(f"Weekday matching stats calculated: {len(filtered_high)} highs, {len(filtered_low)} lows")
            return result

        except Exception as e:
            self.logger.error(f"Error calculating Weekday matching statistics: {e}")
            return {'highs': {}, 'lows': {}, 'filter_type': 'weekday_matching'}

    def _apply_hl_matching_filter(self, high_data: List[Any], low_data: List[Any]) -> Tuple[List[Any], List[Any]]:
        """Apply H/L matching filter using vectorized operations."""
        try:
            if not high_data or not low_data:
                return [], []

            # Get last high row for category matching
            last_high_row = high_data[-1]
            if len(last_high_row) <= 2:
                return high_data, low_data

            # Extract category from last high (column 2)
            last_category = str(last_high_row[2])

            # Vectorized filtering using numpy-style operations
            filtered_high = [row for row in high_data if len(row) > 2 and str(row[2]) == last_category]
            filtered_low = [row for row in low_data if len(row) > 2 and str(row[2]) == last_category]

            return filtered_high, filtered_low

        except Exception as e:
            self.logger.error(f"Error applying H/L matching filter: {e}")
            return high_data, low_data

    def _apply_weekday_matching_filter(self, high_data: List[Any], low_data: List[Any]) -> Tuple[List[Any], List[Any]]:
        """Apply Weekday matching filter using vectorized operations."""
        try:
            if not high_data or not low_data:
                return [], []

            # Get last high row for weekday matching
            last_high_row = high_data[-1]
            if len(last_high_row) <= 1:
                return high_data, low_data

            # Extract weekday from last high (column 1)
            last_weekday = str(last_high_row[1])

            # Vectorized filtering using numpy-style operations
            filtered_high = [row for row in high_data if len(row) > 1 and str(row[1]) == last_weekday]
            filtered_low = [row for row in low_data if len(row) > 1 and str(row[1]) == last_weekday]

            return filtered_high, filtered_low

        except Exception as e:
            self.logger.error(f"Error applying Weekday matching filter: {e}")
            return high_data, low_data

    def _calculate_statistics_vectorized(self, data: List[Any], zone_type: str) -> Dict[str, Any]:
        """Calculate MaxAvg, Avg, and Median statistics using vectorized operations."""
        try:
            if not data:
                return {'max_avg': 0, 'average': 0, 'median': 0, 'count': 0}

            # Extract projected values (column 6) using vectorized operations
            projected_values = np.array([
                float(row[6]) for row in data
                if len(row) > 6 and self._is_numeric(row[6])
            ])

            if len(projected_values) == 0:
                return {'max_avg': 0, 'average': 0, 'median': 0, 'count': 0}

            # Vectorized statistical calculations using numpy
            average = np.mean(projected_values)
            median = np.median(projected_values)

            # Sort for percentile calculations
            sorted_values = np.sort(projected_values)
            n_values = len(sorted_values)

            # MaxAvg calculation depends on zone type (vectorized)
            if zone_type == 'high':
                # For highs: MaxAvg is top 50% average
                top_50_percent = sorted_values[-max(1, n_values // 2):]
                max_avg = np.mean(top_50_percent)
            else:
                # For lows: MaxAvg is bottom 50% average (lowest values)
                bottom_50_percent = sorted_values[:max(1, n_values // 2)]
                max_avg = np.mean(bottom_50_percent)

            return {
                'max_avg': float(max_avg),
                'average': float(average),
                'median': float(median),
                'count': n_values
            }

        except Exception as e:
            self.logger.error(f"Error calculating vectorized statistics for {zone_type}: {e}")
            return {'max_avg': 0, 'average': 0, 'median': 0, 'count': 0}

    def _is_numeric(self, value) -> bool:
        """Check if a value is numeric."""
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False

    def _apply_zone_filtering(
        self,
        hl_matching_stats: Dict[str, Any],
        weekday_matching_stats: Dict[str, Any],
        zone_type: str
    ) -> Dict[str, Any]:
        """
        Apply filtering to select optimal zones by comparing H/L and Weekday matching.

        For upside (high) zones: Use whichever is HIGHER
        For downside (low) zones: Use whichever is LOWER

        Args:
            hl_matching_stats: H/L matching statistics
            weekday_matching_stats: Weekday matching statistics
            zone_type: 'high' or 'low'

        Returns:
            Dictionary with filtered optimal zone values
        """
        try:
            # Get the relevant stats for this zone type
            hl_stats = hl_matching_stats.get(f'{zone_type}s', {})
            weekday_stats = weekday_matching_stats.get(f'{zone_type}s', {})

            if not hl_stats or not weekday_stats:
                # If one set is missing, use the available one
                return hl_stats if hl_stats else weekday_stats

            filtered_zones = {}

            # Apply filtering for each statistic type
            for stat_type in ['max_avg', 'average', 'median']:
                hl_value = hl_stats.get(stat_type, 0)
                weekday_value = weekday_stats.get(stat_type, 0)

                if zone_type == 'high':
                    # For upside zones: Use whichever is HIGHER
                    if hl_value >= weekday_value:
                        filtered_zones[stat_type] = {
                            'value': hl_value,
                            'source': 'H/L_matching',
                            'count': hl_stats.get('count', 0)
                        }
                    else:
                        filtered_zones[stat_type] = {
                            'value': weekday_value,
                            'source': 'weekday_matching',
                            'count': weekday_stats.get('count', 0)
                        }
                else:  # zone_type == 'low'
                    # For downside zones: Use whichever is LOWER
                    if hl_value <= weekday_value:
                        filtered_zones[stat_type] = {
                            'value': hl_value,
                            'source': 'H/L_matching',
                            'count': hl_stats.get('count', 0)
                        }
                    else:
                        filtered_zones[stat_type] = {
                            'value': weekday_value,
                            'source': 'weekday_matching',
                            'count': weekday_stats.get('count', 0)
                        }

            self.logger.debug(f"Applied zone filtering for {zone_type} zones")
            return filtered_zones

        except Exception as e:
            self.logger.error(f"Error applying zone filtering for {zone_type}: {e}")
            # Return H/L stats as fallback
            return hl_matching_stats.get(f'{zone_type}s', {})

    def _extend_zones_by_percentage(
        self,
        filtered_zones: Dict[str, Any],
        zone_type: str,
        extension_percentage: float = 0.033
    ) -> Dict[str, Any]:
        """
        Extend filtered zones by +-0.033% to create zone ranges.

        Args:
            filtered_zones: Dictionary of filtered zone data
            zone_type: 'high' or 'low'
            extension_percentage: Percentage to extend zones (default 0.033%)

        Returns:
            Dictionary with extended zone ranges
        """
        try:
            extended_zones = {}
            extension_factor = extension_percentage / 100.0  # Convert to decimal (0.00033)

            for stat_type, data in filtered_zones.items():
                if isinstance(data, dict) and 'value' in data:
                    base_price = data['value']
                    source = data['source']
                    count = data['count']

                    # Calculate extension amount
                    extension_amount = base_price * extension_factor

                    # Create upper and lower bounds
                    upper_bound = base_price + extension_amount
                    lower_bound = base_price - extension_amount

                    extended_zones[stat_type] = {
                        'base_price': base_price,
                        'upper_bound': upper_bound,
                        'lower_bound': lower_bound,
                        'extension_amount': extension_amount,
                        'extension_percentage': extension_percentage,
                        'source': source,
                        'count': count
                    }
                else:
                    # Handle legacy data format
                    base_price = data if isinstance(data, (int, float)) else 0
                    extension_amount = base_price * extension_factor

                    extended_zones[stat_type] = {
                        'base_price': base_price,
                        'upper_bound': base_price + extension_amount,
                        'lower_bound': base_price - extension_amount,
                        'extension_amount': extension_amount,
                        'extension_percentage': extension_percentage,
                        'source': 'unknown',
                        'count': 0
                    }

            self.logger.debug(f"Extended {len(extended_zones)} {zone_type} zones by ±{extension_percentage}%")
            return extended_zones

        except Exception as e:
            self.logger.error(f"Error extending zones by percentage for {zone_type}: {e}")
            return filtered_zones  # Return original data on error

    def _create_zones_from_extended_data(
        self,
        extended_data: Dict[str, Any],
        zone_type: str
    ) -> List[ZoneData]:
        """Create zone objects from extended zone data with upper/lower bounds."""
        try:
            zones = []

            # Create zones for each extended statistic
            for stat_type, data in extended_data.items():
                if isinstance(data, dict) and 'base_price' in data:
                    base_price = data['base_price']
                    upper_bound = data['upper_bound']
                    lower_bound = data['lower_bound']
                    source = data['source']
                    count = data['count']
                    extension_pct = data.get('extension_percentage', 0.033)

                    # Create base zone
                    base_zone = ZoneData(
                        price=base_price,
                        zone_type=zone_type,
                        strength=1,
                        count=count,
                        winrate=0.0,
                        level_name=f"{source}_{stat_type}_{zone_type}_base"
                    )
                    zones.append(base_zone)

                    # Create upper bound zone
                    upper_zone = ZoneData(
                        price=upper_bound,
                        zone_type=zone_type,
                        strength=1,
                        count=count,
                        winrate=0.0,
                        level_name=f"{source}_{stat_type}_{zone_type}_upper_+{extension_pct}%"
                    )
                    zones.append(upper_zone)

                    # Create lower bound zone
                    lower_zone = ZoneData(
                        price=lower_bound,
                        zone_type=zone_type,
                        strength=1,
                        count=count,
                        winrate=0.0,
                        level_name=f"{source}_{stat_type}_{zone_type}_lower_-{extension_pct}%"
                    )
                    zones.append(lower_zone)

                else:
                    # Handle legacy data format (fallback)
                    price = data if isinstance(data, (int, float)) else 0
                    zone = ZoneData(
                        price=price,
                        zone_type=zone_type,
                        strength=1,
                        count=0,
                        winrate=0.0,
                        level_name=f"{stat_type}_{zone_type}_legacy"
                    )
                    zones.append(zone)

            self.logger.debug(f"Created {len(zones)} extended {zone_type} zones")
            return zones

        except Exception as e:
            self.logger.error(f"Error creating zones from extended data for {zone_type}: {e}")
            return []

    def _create_zones_from_filtered_data(
        self,
        filtered_data: Dict[str, Any],
        zone_type: str
    ) -> List[ZoneData]:
        """Create zone objects from filtered optimal data."""
        try:
            zones = []

            # Create zones for each filtered statistic
            for stat_type, data in filtered_data.items():
                if isinstance(data, dict) and 'value' in data:
                    # This is filtered data with source information
                    price = data['value']
                    source = data['source']
                    count = data['count']
                    level_name = f"{source}_{stat_type}_{zone_type}"
                else:
                    # This is legacy data format (fallback)
                    price = data
                    source = "unknown"
                    count = 0
                    level_name = f"{stat_type}_{zone_type}"

                zone = ZoneData(
                    price=price,
                    zone_type=zone_type,
                    strength=1,
                    count=count,
                    winrate=0.0,
                    level_name=level_name
                )
                zones.append(zone)

            self.logger.debug(f"Created {len(zones)} filtered {zone_type} zones")
            return zones

        except Exception as e:
            self.logger.error(f"Error creating zones from filtered data for {zone_type}: {e}")
            return []

    def _create_high_zones_from_statistics(
        self,
        hl_matching_stats: Dict[str, Any],
        weekday_matching_stats: Dict[str, Any]
    ) -> List[ZoneData]:
        """Create high zone objects from calculated statistics."""
        try:
            zones = []

            # Create H/L matching high zones
            hl_highs = hl_matching_stats.get('highs', {})
            if hl_highs:
                zones.extend([
                    ZoneData(
                        price=hl_highs.get('max_avg', 0),
                        zone_type='high',
                        strength=1,
                        count=hl_highs.get('count', 0),
                        winrate=0.0,
                        level_name='H/L_MaxAvg_High'
                    ),
                    ZoneData(
                        price=hl_highs.get('average', 0),
                        zone_type='high',
                        strength=1,
                        count=hl_highs.get('count', 0),
                        winrate=0.0,
                        level_name='H/L_Avg_High'
                    ),
                    ZoneData(
                        price=hl_highs.get('median', 0),
                        zone_type='high',
                        strength=1,
                        count=hl_highs.get('count', 0),
                        winrate=0.0,
                        level_name='H/L_Median_High'
                    )
                ])

            # Create Weekday matching high zones
            weekday_highs = weekday_matching_stats.get('highs', {})
            if weekday_highs:
                zones.extend([
                    ZoneData(
                        price=weekday_highs.get('max_avg', 0),
                        zone_type='high',
                        strength=1,
                        count=weekday_highs.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_MaxAvg_High'
                    ),
                    ZoneData(
                        price=weekday_highs.get('average', 0),
                        zone_type='high',
                        strength=1,
                        count=weekday_highs.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_Avg_High'
                    ),
                    ZoneData(
                        price=weekday_highs.get('median', 0),
                        zone_type='high',
                        strength=1,
                        count=weekday_highs.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_Median_High'
                    )
                ])

            self.logger.debug(f"Created {len(zones)} high zones from statistics")
            return zones

        except Exception as e:
            self.logger.error(f"Error creating high zones from statistics: {e}")
            return []

    def _create_low_zones_from_statistics(
        self,
        hl_matching_stats: Dict[str, Any],
        weekday_matching_stats: Dict[str, Any]
    ) -> List[ZoneData]:
        """Create low zone objects from calculated statistics."""
        try:
            zones = []

            # Create H/L matching low zones
            hl_lows = hl_matching_stats.get('lows', {})
            if hl_lows:
                zones.extend([
                    ZoneData(
                        price=hl_lows.get('max_avg', 0),
                        zone_type='low',
                        strength=1,
                        count=hl_lows.get('count', 0),
                        winrate=0.0,
                        level_name='H/L_MaxAvg_Low'
                    ),
                    ZoneData(
                        price=hl_lows.get('average', 0),
                        zone_type='low',
                        strength=1,
                        count=hl_lows.get('count', 0),
                        winrate=0.0,
                        level_name='H/L_Avg_Low'
                    ),
                    ZoneData(
                        price=hl_lows.get('median', 0),
                        zone_type='low',
                        strength=1,
                        count=hl_lows.get('count', 0),
                        winrate=0.0,
                        level_name='H/L_Median_Low'
                    )
                ])

            # Create Weekday matching low zones
            weekday_lows = weekday_matching_stats.get('lows', {})
            if weekday_lows:
                zones.extend([
                    ZoneData(
                        price=weekday_lows.get('max_avg', 0),
                        zone_type='low',
                        strength=1,
                        count=weekday_lows.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_MaxAvg_Low'
                    ),
                    ZoneData(
                        price=weekday_lows.get('average', 0),
                        zone_type='low',
                        strength=1,
                        count=weekday_lows.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_Avg_Low'
                    ),
                    ZoneData(
                        price=weekday_lows.get('median', 0),
                        zone_type='low',
                        strength=1,
                        count=weekday_lows.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_Median_Low'
                    )
                ])

            self.logger.debug(f"Created {len(zones)} low zones from statistics")
            return zones

        except Exception as e:
            self.logger.error(f"Error creating low zones from statistics: {e}")
            return []
    
    def get_zones_for_display(
        self, 
        zones_result: ZoneCalculationResult,
        filter_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Format zones for display in the frontend.
        
        Args:
            zones_result: Result from zone calculations
            filter_params: Optional filtering parameters
            
        Returns:
            Dictionary formatted for frontend display
        """
        try:
            # Apply any filtering if specified
            filtered_zones = self._apply_zone_filters(zones_result.zones, filter_params)
            
            # Format for display
            display_data = {
                "zones": [self._format_zone_for_display(zone) for zone in filtered_zones],
                "high_zones": [self._format_zone_for_display(zone) for zone in zones_result.high_zones],
                "low_zones": [self._format_zone_for_display(zone) for zone in zones_result.low_zones],
                "metadata": zones_result.calculation_metadata
            }
            
            return display_data
            
        except Exception as e:
            self.logger.error(f"Error formatting zones for display: {e}")
            return {"zones": [], "high_zones": [], "low_zones": [], "metadata": {}}
    
    def _apply_zone_filters(
        self, 
        zones: List[ZoneData], 
        filter_params: Optional[Dict[str, Any]]
    ) -> List[ZoneData]:
        """Apply filtering to zones based on parameters."""
        if not filter_params:
            return zones
            
        filtered = zones
        
        # TODO: Implement filtering logic based on requirements
        # Examples: minimum strength, minimum count, winrate thresholds, etc.
        
        return filtered
    
    def _format_zone_for_display(self, zone: ZoneData) -> Dict[str, Any]:
        """Format a single zone for frontend display."""
        return {
            "price": zone.price,
            "zone_type": zone.zone_type,
            "strength": zone.strength,
            "count": zone.count,
            "winrate": zone.winrate,
            "level_name": zone.level_name
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp for metadata."""
        import time
        return str(int(time.time()))
    
    def clear_cache(self):
        """Clear the zone calculation cache."""
        self._zone_cache.clear()
        self.logger.debug("Zone calculation cache cleared")
