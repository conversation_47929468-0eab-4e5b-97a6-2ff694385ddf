"""
Volgraph Zones Service - Backend calculation of volatility zones.

This module handles the calculation of volatility zones for the volgraph system.
All zone calculations are performed in the backend to maintain separation of concerns
and ensure consistent, high-performance computations.
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from .observability import get_structured_logger, trace_operation


@dataclass
class ZoneData:
    """Data structure for a volatility zone."""
    price: float
    zone_type: str  # 'high' or 'low'
    strength: int
    count: int
    winrate: float
    level_name: str


@dataclass
class ZoneCalculationResult:
    """Result of zone calculations."""
    zones: List[ZoneData]
    high_zones: List[ZoneData]
    low_zones: List[ZoneData]
    calculation_metadata: Dict[str, Any]


class VolgraphZonesService:
    """
    Service for calculating volatility zones in the backend.
    
    This service handles all zone-related calculations including:
    - Zone identification and classification
    - Strength calculations
    - Winrate analysis
    - Zone filtering and ranking
    """
    
    def __init__(self):
        self.logger = get_structured_logger("VolgraphZonesService")
        self._zone_cache: Dict[str, ZoneCalculationResult] = {}
        
    def calculate_zones(
        self,
        high_data: List[Dict[str, Any]],
        low_data: List[Dict[str, Any]],
        market_data: Optional[Dict[str, Any]] = None,
        calculation_params: Optional[Dict[str, Any]] = None
    ) -> ZoneCalculationResult:
        """
        Calculate volatility zones from high and low data.

        Args:
            high_data: List of high price data points
            low_data: List of low price data points
            market_data: Optional market data context
            calculation_params: Optional parameters containing H/L and Weekday matching data

        Returns:
            ZoneCalculationResult containing all calculated zones
        """
        with trace_operation("calculate_zones"):
            try:
                self.logger.info("Starting zone calculations")

                # Initialize default parameters
                params = calculation_params or {}

                # Extract cached zone data from parameters
                hl_matching_data = params.get('hl_matching', {})
                weekday_matching_data = params.get('weekday_matching', {})

                # Calculate zones from cached statistics
                high_zones = self._calculate_high_zones_from_cached_data(hl_matching_data, weekday_matching_data)
                low_zones = self._calculate_low_zones_from_cached_data(hl_matching_data, weekday_matching_data)

                # Combine all zones
                all_zones = high_zones + low_zones

                # Create calculation metadata
                metadata = {
                    "total_zones": len(all_zones),
                    "high_zones_count": len(high_zones),
                    "low_zones_count": len(low_zones),
                    "calculation_timestamp": self._get_timestamp(),
                    "parameters_used": params,
                    "hl_matching_stats": hl_matching_data,
                    "weekday_matching_stats": weekday_matching_data
                }

                result = ZoneCalculationResult(
                    zones=all_zones,
                    high_zones=high_zones,
                    low_zones=low_zones,
                    calculation_metadata=metadata
                )

                self.logger.info(f"Zone calculations completed: {len(all_zones)} total zones")
                return result

            except Exception as e:
                self.logger.error(f"Error calculating zones: {e}")
                # Return empty result on error
                return ZoneCalculationResult(
                    zones=[],
                    high_zones=[],
                    low_zones=[],
                    calculation_metadata={"error": str(e)}
                )
    
    def _calculate_high_zones_from_cached_data(
        self,
        hl_matching_data: Dict[str, Any],
        weekday_matching_data: Dict[str, Any]
    ) -> List[ZoneData]:
        """Calculate high zones from cached H/L and Weekday matching data."""
        try:
            zones = []

            # Extract high zone statistics from H/L matching
            hl_highs = hl_matching_data.get('highs', {})
            if hl_highs:
                # Create zones for H/L matching MaxAvg, Avg, Median
                zones.extend([
                    ZoneData(
                        price=hl_highs.get('max_avg', 0),
                        zone_type='high',
                        strength=1,
                        count=hl_highs.get('count', 0),
                        winrate=0.0,  # Will be calculated later if needed
                        level_name='H/L_MaxAvg_High'
                    ),
                    ZoneData(
                        price=hl_highs.get('average', 0),
                        zone_type='high',
                        strength=1,
                        count=hl_highs.get('count', 0),
                        winrate=0.0,
                        level_name='H/L_Avg_High'
                    ),
                    ZoneData(
                        price=hl_highs.get('median', 0),
                        zone_type='high',
                        strength=1,
                        count=hl_highs.get('count', 0),
                        winrate=0.0,
                        level_name='H/L_Median_High'
                    )
                ])

            # Extract high zone statistics from Weekday matching
            weekday_highs = weekday_matching_data.get('highs', {})
            if weekday_highs:
                # Create zones for Weekday matching MaxAvg, Avg, Median
                zones.extend([
                    ZoneData(
                        price=weekday_highs.get('max_avg', 0),
                        zone_type='high',
                        strength=1,
                        count=weekday_highs.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_MaxAvg_High'
                    ),
                    ZoneData(
                        price=weekday_highs.get('average', 0),
                        zone_type='high',
                        strength=1,
                        count=weekday_highs.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_Avg_High'
                    ),
                    ZoneData(
                        price=weekday_highs.get('median', 0),
                        zone_type='high',
                        strength=1,
                        count=weekday_highs.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_Median_High'
                    )
                ])

            self.logger.debug(f"Calculated {len(zones)} high zones from cached data")
            return zones

        except Exception as e:
            self.logger.error(f"Error calculating high zones from cached data: {e}")
            return []

    def _calculate_low_zones_from_cached_data(
        self,
        hl_matching_data: Dict[str, Any],
        weekday_matching_data: Dict[str, Any]
    ) -> List[ZoneData]:
        """Calculate low zones from cached H/L and Weekday matching data."""
        try:
            zones = []

            # Extract low zone statistics from H/L matching
            hl_lows = hl_matching_data.get('lows', {})
            if hl_lows:
                # Create zones for H/L matching MaxAvg, Avg, Median
                zones.extend([
                    ZoneData(
                        price=hl_lows.get('max_avg', 0),
                        zone_type='low',
                        strength=1,
                        count=hl_lows.get('count', 0),
                        winrate=0.0,
                        level_name='H/L_MaxAvg_Low'
                    ),
                    ZoneData(
                        price=hl_lows.get('average', 0),
                        zone_type='low',
                        strength=1,
                        count=hl_lows.get('count', 0),
                        winrate=0.0,
                        level_name='H/L_Avg_Low'
                    ),
                    ZoneData(
                        price=hl_lows.get('median', 0),
                        zone_type='low',
                        strength=1,
                        count=hl_lows.get('count', 0),
                        winrate=0.0,
                        level_name='H/L_Median_Low'
                    )
                ])

            # Extract low zone statistics from Weekday matching
            weekday_lows = weekday_matching_data.get('lows', {})
            if weekday_lows:
                # Create zones for Weekday matching MaxAvg, Avg, Median
                zones.extend([
                    ZoneData(
                        price=weekday_lows.get('max_avg', 0),
                        zone_type='low',
                        strength=1,
                        count=weekday_lows.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_MaxAvg_Low'
                    ),
                    ZoneData(
                        price=weekday_lows.get('average', 0),
                        zone_type='low',
                        strength=1,
                        count=weekday_lows.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_Avg_Low'
                    ),
                    ZoneData(
                        price=weekday_lows.get('median', 0),
                        zone_type='low',
                        strength=1,
                        count=weekday_lows.get('count', 0),
                        winrate=0.0,
                        level_name='Weekday_Median_Low'
                    )
                ])

            self.logger.debug(f"Calculated {len(zones)} low zones from cached data")
            return zones

        except Exception as e:
            self.logger.error(f"Error calculating low zones from cached data: {e}")
            return []
    
    def get_zones_for_display(
        self, 
        zones_result: ZoneCalculationResult,
        filter_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Format zones for display in the frontend.
        
        Args:
            zones_result: Result from zone calculations
            filter_params: Optional filtering parameters
            
        Returns:
            Dictionary formatted for frontend display
        """
        try:
            # Apply any filtering if specified
            filtered_zones = self._apply_zone_filters(zones_result.zones, filter_params)
            
            # Format for display
            display_data = {
                "zones": [self._format_zone_for_display(zone) for zone in filtered_zones],
                "high_zones": [self._format_zone_for_display(zone) for zone in zones_result.high_zones],
                "low_zones": [self._format_zone_for_display(zone) for zone in zones_result.low_zones],
                "metadata": zones_result.calculation_metadata
            }
            
            return display_data
            
        except Exception as e:
            self.logger.error(f"Error formatting zones for display: {e}")
            return {"zones": [], "high_zones": [], "low_zones": [], "metadata": {}}
    
    def _apply_zone_filters(
        self, 
        zones: List[ZoneData], 
        filter_params: Optional[Dict[str, Any]]
    ) -> List[ZoneData]:
        """Apply filtering to zones based on parameters."""
        if not filter_params:
            return zones
            
        filtered = zones
        
        # TODO: Implement filtering logic based on requirements
        # Examples: minimum strength, minimum count, winrate thresholds, etc.
        
        return filtered
    
    def _format_zone_for_display(self, zone: ZoneData) -> Dict[str, Any]:
        """Format a single zone for frontend display."""
        return {
            "price": zone.price,
            "zone_type": zone.zone_type,
            "strength": zone.strength,
            "count": zone.count,
            "winrate": zone.winrate,
            "level_name": zone.level_name
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp for metadata."""
        import time
        return str(int(time.time()))
    
    def clear_cache(self):
        """Clear the zone calculation cache."""
        self._zone_cache.clear()
        self.logger.debug("Zone calculation cache cleared")
