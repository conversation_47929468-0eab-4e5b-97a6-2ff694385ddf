<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">9%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.4">coverage.py v7.10.4</a>,
            created at 2025-08-25 22:53 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7___init___py.html">src\backend\__init__.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t35">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t35"><data value='array_interface__'>BufferProtocol.__array_interface__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t36">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t36"><data value='buffer__'>BufferProtocol.__buffer__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t45">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t45"><data value='init__'>ZeroCopyBufferManager.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t49">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t49"><data value='setup_logging'>ZeroCopyBufferManager._setup_logging</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t65">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t65"><data value='get_buffer_view'>ZeroCopyBufferManager.get_buffer_view</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t90">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t90"><data value='prepare_for_cpp_kernel'>ZeroCopyBufferManager.prepare_for_cpp_kernel</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t118">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t118"><data value='safe_to_list'>ZeroCopyBufferManager._safe_to_list</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t133">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t133"><data value='call_cpp_kernel_with_optimal_buffers'>ZeroCopyBufferManager.call_cpp_kernel_with_optimal_buffers</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t143">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t143"><data value='init__'>ZeroCopyBufferManager.call_cpp_kernel_with_optimal_buffers.MockResult.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t188">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t188"><data value='optimize_data_for_serialization'>ZeroCopyBufferManager.optimize_data_for_serialization</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t210">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t210"><data value='clear_cache'>ZeroCopyBufferManager.clear_cache</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>8</td>
                <td>3</td>
                <td class="right" data-ratio="22 30">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t22">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t22"><data value='init__'>ChartDataModel.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t29">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t29"><data value='setup_logging'>ChartDataModel._setup_logging</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t45">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t45"><data value='update_data'>ChartDataModel.update_data</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t58">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t58"><data value='prepare_render_data'>ChartDataModel._prepare_render_data</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t92">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t92"><data value='get_candlestick_data'>ChartDataModel.get_candlestick_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t96">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t96"><data value='get_rebased_candlestick_data'>ChartDataModel.get_rebased_candlestick_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t100">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t100"><data value='get_bounds'>ChartDataModel.get_bounds</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t104">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t104"><data value='get_donchian_data'>ChartDataModel.get_donchian_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t108">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t108"><data value='get_vector_data'>ChartDataModel.get_vector_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t112">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t112"><data value='get_peak_trough_data'>ChartDataModel.get_peak_trough_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t118">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t118"><data value='get_stdev_lines_data'>ChartDataModel.get_stdev_lines_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t122">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t122"><data value='get_mean_levels_data'>ChartDataModel.get_mean_levels_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t126">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t126"><data value='get_predictive_cycle_data'>ChartDataModel.get_predictive_cycle_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t130">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t130"><data value='get_crosshair_info'>ChartDataModel.get_crosshair_info</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t135">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t135"><data value='has_data'>ChartDataModel.has_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t139">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t139"><data value='clear'>ChartDataModel.clear</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t153">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t153"><data value='init__'>CandlestickRenderItem.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t157">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t157"><data value='extract_bounds'>CandlestickRenderItem._extract_bounds</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t182">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t182"><data value='get_bounds'>CandlestickRenderItem.get_bounds</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t186">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t186"><data value='get_render_data'>CandlestickRenderItem.get_render_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t199">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t199"><data value='init__'>BandwidthChartModel.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t208">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t208"><data value='update_data'>BandwidthChartModel.update_data</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t227">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t227"><data value='get_bandwidth_data'>BandwidthChartModel.get_bandwidth_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t231">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t231"><data value='get_strip_segments'>BandwidthChartModel.get_strip_segments</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t235">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t235"><data value='get_percentage_levels'>BandwidthChartModel.get_percentage_levels</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t239">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t239"><data value='has_movement'>BandwidthChartModel.has_movement</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t243">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t243"><data value='get_strip_position'>BandwidthChartModel.get_strip_position</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t247">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t247"><data value='has_data'>BandwidthChartModel.has_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t42">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t42"><data value='init__'>ComputationWorker.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t48">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t48"><data value='setup_logging'>ComputationWorker._setup_logging</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t64">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t64"><data value='run'>ComputationWorker.run</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t93">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t93"><data value='perform_computation'>ComputationWorker._perform_computation</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t110">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t110"><data value='calculate_bounds'>ComputationWorker._calculate_bounds</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t139">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t139"><data value='calculate_distance'>ComputationWorker._calculate_distance</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t152">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t152"><data value='detect_range'>ComputationWorker._detect_range</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t174">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t174"><data value='map_position'>ComputationWorker._map_position</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t217">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t217"><data value='determine_color'>ComputationWorker._determine_color</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t248">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t248"><data value='check_visibility'>ComputationWorker._check_visibility</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t303">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t303"><data value='init__'>ComputationOffloader.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t330">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t330"><data value='setup_logging'>ComputationOffloader._setup_logging</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t346">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t346"><data value='submit_computation'>ComputationOffloader.submit_computation</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t388">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t388"><data value='on_computation_completed'>ComputationOffloader._on_computation_completed</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t417">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t417"><data value='on_computation_failed'>ComputationOffloader._on_computation_failed</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t427">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t427"><data value='get_stats'>ComputationOffloader.get_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t436">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t436"><data value='shutdown'>ComputationOffloader.shutdown</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t52">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t52"><data value='init__'>DataService.__init__</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t82">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t82"><data value='get_options_data'>DataService.get_options_data</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t90">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t90"><data value='clear_options_cache'>DataService.clear_options_cache</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t98">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t98"><data value='get_options_cache_info'>DataService.get_options_cache_info</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t106">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t106"><data value='calculate_volatility_data'>DataService.calculate_volatility_data</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t117">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t117"><data value='calculate_density_data'>DataService.calculate_density_data</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t129">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t129"><data value='post_init__'>DataService.__post_init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t134">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t134"><data value='setup_logging'>DataService._setup_logging</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t150">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t150"><data value='initialize'>DataService.initialize</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t154">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t154"><data value='shutdown'>DataService.shutdown</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t171">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t171"><data value='get_yfinance_interval'>DataService._get_yfinance_interval</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t183">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t183"><data value='get_candles_per_day'>DataService._get_candles_per_day</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t196">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t196"><data value='get_period_from_dtl'>DataService._get_period_from_dtl</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t220">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t220"><data value='fetch_data_sync'>DataService._fetch_data_sync</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t273">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t273"><data value='calculate_donchian_midpoint'>DataService._calculate_donchian_midpoint</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t294">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t294"><data value='calculate_donchian_midpoint_with_skip'>DataService._calculate_donchian_midpoint_with_skip</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t319">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t319"><data value='calculate_bollinger_bandwidth'>DataService._calculate_bollinger_bandwidth</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t342">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t342"><data value='calculate_bollinger_bandwidth_with_skip'>DataService._calculate_bollinger_bandwidth_with_skip</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t367">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t367"><data value='calculate_donchian_strip_segments'>DataService._calculate_donchian_strip_segments</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t398">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t398"><data value='calculate_donchian_strip_segments_with_skip'>DataService._calculate_donchian_strip_segments_with_skip</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t422">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t422"><data value='track_vector_direction_changes'>DataService._track_vector_direction_changes</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t443">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t443"><data value='calculate_rebased_prices'>DataService._calculate_rebased_prices</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t472">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t472"><data value='apply_skip_logic_to_rebased_prices'>DataService._apply_skip_logic_to_rebased_prices</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t512">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t512"><data value='calculate_peaks_and_troughs'>DataService._calculate_peaks_and_troughs</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t542">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t542"><data value='calculate_standard_deviations'>DataService._calculate_standard_deviations</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t577">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t577"><data value='calculate_mean_peak_trough_strengths'>DataService._calculate_mean_peak_trough_strengths</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t626">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t626"><data value='calculate_mean_peak_trough_levels'>DataService._calculate_mean_peak_trough_levels</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t652">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t652"><data value='calculate_predictive_cycle_lines'>DataService._calculate_predictive_cycle_lines</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t676">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t676"><data value='calculate_stdev_infinity_lines'>DataService._calculate_stdev_infinity_lines</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t700">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t700"><data value='calculate_crosshair_info_lookup'>DataService._calculate_crosshair_info_lookup</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t728">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t728"><data value='fix_cycle_position_calculation'>DataService._fix_cycle_position_calculation</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t780">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t780"><data value='calculate_correct_cycle_position'>DataService._calculate_correct_cycle_position</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t815">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t815"><data value='calculate_cycle_position_backend'>DataService._calculate_cycle_position_backend</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t833">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t833"><data value='calculate_percentage_levels'>DataService._calculate_percentage_levels</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t851">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t851"><data value='calculate_zero_percent_price'>DataService._calculate_zero_percent_price</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t871">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t871"><data value='prepare_peak_trough_business_data'>DataService._prepare_peak_trough_business_data</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t954">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t954"><data value='prepare_serializable_data'>DataService._prepare_serializable_data</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1007">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1007"><data value='fetch_market_data'>DataService.fetch_market_data</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1034">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1034"><data value='fetch_market_data_impl'>DataService._fetch_market_data_impl</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1088">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1088"><data value='fetch_market_data_with_rebasing'>DataService.fetch_market_data_with_rebasing</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1153">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1153"><data value='fetch_market_data_with_rebasing_impl'>DataService._fetch_market_data_with_rebasing_impl</data></a></td>
                <td>192</td>
                <td>192</td>
                <td>0</td>
                <td class="right" data-ratio="0 192">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1551">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1551"><data value='prepare_volatility_statistics_data'>DataService._prepare_volatility_statistics_data</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1579">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1579"><data value='apply_volatility_statistics_filter'>DataService.apply_volatility_statistics_filter</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1597">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1597"><data value='apply_volatility_statistics_filter_py'>DataService._apply_volatility_statistics_filter_py</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1634">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1634"><data value='apply_fwl_aggr_logic'>DataService._apply_fwl_aggr_logic</data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1740">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t1740"><data value='get_current_fwl_aggr'>DataService.get_current_fwl_aggr</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>75</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="72 75">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t14">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t14"><data value='init__'>DensityCalculationsService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t17">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t17"><data value='calculate_option_averages'>DensityCalculationsService.calculate_option_averages</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t96">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t96"><data value='calculate_complex_divisor'>DensityCalculationsService.calculate_complex_divisor</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t126">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t126"><data value='get_filtered_strikes_and_options_data'>DensityCalculationsService.get_filtered_strikes_and_options_data</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t170">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t170"><data value='calculate_options_averages_for_strikes'>DensityCalculationsService.calculate_options_averages_for_strikes</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t235">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t235"><data value='get_options_data_for_strike'>DensityCalculationsService.get_options_data_for_strike</data></a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t263">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t263"><data value='is_nan_like'>DensityCalculationsService.get_options_data_for_strike._is_nan_like</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t325">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t325"><data value='calculate_density_data'>DensityCalculationsService.calculate_density_data</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t434">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t434"><data value='calculate_title_info'>DensityCalculationsService._calculate_title_info</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t465">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t465"><data value='extract_current_close'>DensityCalculationsService._extract_current_close</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t481">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t481"><data value='extract_projected_data'>DensityCalculationsService._extract_projected_data</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t518">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t518"><data value='calculate_highs_statistics'>DensityCalculationsService._calculate_highs_statistics</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t547">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t547"><data value='calculate_lows_statistics'>DensityCalculationsService._calculate_lows_statistics</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t576">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t576"><data value='calculate_axis_limits'>DensityCalculationsService._calculate_axis_limits</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t594">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t594"><data value='calculate_axis_limits'>DensityCalculationsService.calculate_axis_limits</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t598">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t598"><data value='calculate_title_position'>DensityCalculationsService.calculate_title_position</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t602">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t602"><data value='calculate_current_price_label_y'>DensityCalculationsService.calculate_current_price_label_y</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t611">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t611"><data value='calculate_title_position'>DensityCalculationsService._calculate_title_position</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t625">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t625"><data value='calculate_strikes_title_y'>DensityCalculationsService.calculate_strikes_title_y</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t634">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t634"><data value='get_options_data_from_market_data'>DensityCalculationsService._get_options_data_from_market_data</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t647">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t647"><data value='compute_profit_loss_curves'>DensityCalculationsService.compute_profit_loss_curves</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t700">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t700"><data value='is_valid_price'>DensityCalculationsService.compute_profit_loss_curves._is_valid_price</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t29">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t29"><data value='post_init__'>MetricDefinition.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t40">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t40"><data value='init__'>PrometheusMetricsExporter.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t48">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t48"><data value='register_standard_metrics'>PrometheusMetricsExporter._register_standard_metrics</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t109">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t109"><data value='register_custom_metric'>PrometheusMetricsExporter.register_custom_metric</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t114">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t114"><data value='format_metric_name'>PrometheusMetricsExporter._format_metric_name</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t122">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t122"><data value='get_system_metrics'>PrometheusMetricsExporter._get_system_metrics</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t137">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t137"><data value='get_shared_memory_metrics'>PrometheusMetricsExporter._get_shared_memory_metrics</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t154">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t154"><data value='export_metrics'>PrometheusMetricsExporter.export_metrics</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t243">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t243"><data value='init__'>MetricsServer.__init__</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t260">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t260"><data value='setup_routes'>MetricsServer._setup_routes</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t271">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t271"><data value='logging_middleware'>MetricsServer._logging_middleware</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t302">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t302"><data value='error_middleware'>MetricsServer._error_middleware</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t314">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t314"><data value='metrics_handler'>MetricsServer._metrics_handler</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t328">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t328"><data value='health_handler'>MetricsServer._health_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t339">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t339"><data value='readiness_handler'>MetricsServer._readiness_handler</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t356">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t356"><data value='info_handler'>MetricsServer._info_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t372">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t372"><data value='start'>MetricsServer.start</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t393">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t393"><data value='stop'>MetricsServer.stop</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t411">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t411"><data value='start_metrics_server'>start_metrics_server</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t422">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t422"><data value='stop_metrics_server'>stop_metrics_server</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t60">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t60"><data value='init__'>ChartDataModel.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t78">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t78"><data value='setup_logging'>ChartDataModel._setup_logging</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t94">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t94"><data value='rowCount'>ChartDataModel.rowCount</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t98">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t98"><data value='columnCount'>ChartDataModel.columnCount</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t102">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t102"><data value='data'>ChartDataModel.data</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t127">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t127"><data value='headerData'>ChartDataModel.headerData</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t143">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t143"><data value='add_element'>ChartDataModel.add_element</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t179">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t179"><data value='remove_element'>ChartDataModel.remove_element</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t215">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t215"><data value='update_element'>ChartDataModel.update_element</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t257">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t257"><data value='get_element'>ChartDataModel.get_element</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t261">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t261"><data value='get_elements_by_type'>ChartDataModel.get_elements_by_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t271">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t271"><data value='get_visible_elements'>ChartDataModel.get_visible_elements</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t276">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t276"><data value='get_bounds'>ChartDataModel.get_bounds</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t280">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t280"><data value='get_dirty_regions'>ChartDataModel.get_dirty_regions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t284">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t284"><data value='clear_dirty_regions'>ChartDataModel.clear_dirty_regions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t288">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t288"><data value='update_bounds_cache'>ChartDataModel._update_bounds_cache</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t310">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t310"><data value='recalculate_bounds_cache'>ChartDataModel._recalculate_bounds_cache</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t329">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t329"><data value='schedule_batch_update'>ChartDataModel._schedule_batch_update</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t337">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t337"><data value='process_batch_updates'>ChartDataModel._process_batch_updates</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t364">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t364"><data value='clear_all'>ChartDataModel.clear_all</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t30">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t30"><data value='post_init__'>CorrelationContext.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t38">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t38"><data value='init__'>CorrelationManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t43">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t43"><data value='create_context'>CorrelationManager.create_context</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t63">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t63"><data value='set_current_context'>CorrelationManager.set_current_context</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t67">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t67"><data value='get_current_context'>CorrelationManager.get_current_context</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t71">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t71"><data value='clear_context'>CorrelationManager.clear_context</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t83">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t83"><data value='trace_operation'>CorrelationManager.trace_operation</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t100">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t100"><data value='init__'>MetricsCollector.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t111">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t111"><data value='increment_counter'>MetricsCollector.increment_counter</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t119">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t119"><data value='record_histogram'>MetricsCollector.record_histogram</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t131">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t131"><data value='set_gauge'>MetricsCollector.set_gauge</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t139">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t139"><data value='record_request_duration'>MetricsCollector.record_request_duration</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t150">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t150"><data value='get_percentile'>MetricsCollector.get_percentile</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t164">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t164"><data value='get_metrics_summary'>MetricsCollector.get_metrics_summary</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t194">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t194"><data value='format_metric_name'>MetricsCollector._format_metric_name</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t202">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t202"><data value='calculate_percentile'>MetricsCollector._calculate_percentile</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t215">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t215"><data value='init__'>StructuredLogger.__init__</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t234">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t234"><data value='info'>StructuredLogger.info</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t238">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t238"><data value='warning'>StructuredLogger.warning</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t242">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t242"><data value='error'>StructuredLogger.error</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t249">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t249"><data value='debug'>StructuredLogger.debug</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t253">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t253"><data value='log'>StructuredLogger._log</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t280">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t280"><data value='init__'>StructuredFormatter.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t284">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t284"><data value='format'>StructuredFormatter.format</data></a></td>
                <td>15</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="11 15">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t346">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t346"><data value='get_structured_logger'>get_structured_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t352">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t352"><data value='trace_operation'>trace_operation</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>53</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="53 53">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t19">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t19"><data value='init__'>OptionsService.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t26">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t26"><data value='fetch_options_data'>OptionsService.fetch_options_data</data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t147">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t147"><data value='parse_optioncharts_data'>OptionsService._parse_optioncharts_data</data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t281">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t281"><data value='parse_options_table'>OptionsService._parse_options_table</data></a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t397">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t397"><data value='fetch_available_expiry_dates'>OptionsService._fetch_available_expiry_dates</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t430">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t430"><data value='extract_expiry_dates_from_page'>OptionsService._extract_expiry_dates_from_page</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t478">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t478"><data value='get_asset_class'>OptionsService.get_asset_class</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t495">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t495"><data value='clear_cache'>OptionsService.clear_cache</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t501">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t501"><data value='get_cache_info'>OptionsService.get_cache_info</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_rebasing_py.html#t42">src\backend\rebasing.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_rebasing_py.html#t42"><data value='track_vector_direction_changes_cpp'>track_vector_direction_changes_cpp</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_rebasing_py.html#t117">src\backend\rebasing.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_rebasing_py.html#t117"><data value='calculate_rebased_prices_cpp'>calculate_rebased_prices_cpp</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_rebasing_py.html#t219">src\backend\rebasing.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_rebasing_py.html#t219"><data value='calculate_donchian_rebasing'>calculate_donchian_rebasing</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_rebasing_py.html">src\backend\rebasing.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_rebasing_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t21">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t21"><data value='init__'>ResourceManager.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t32">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t32"><data value='setup_logging'>ResourceManager._setup_logging</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t48">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t48"><data value='initialize_resources'>ResourceManager.initialize_resources</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t83">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t83"><data value='initialize_fallback_resources'>ResourceManager._initialize_fallback_resources</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t111">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t111"><data value='get_pixmap'>ResourceManager.get_pixmap</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t164">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t164"><data value='get_icon'>ResourceManager.get_icon</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t196">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t196"><data value='get_stylesheet'>ResourceManager.get_stylesheet</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t238">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t238"><data value='load_font'>ResourceManager.load_font</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t313">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t313"><data value='get_fallback_path'>ResourceManager._get_fallback_path</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t323">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t323"><data value='clear_cache'>ResourceManager.clear_cache</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t332">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t332"><data value='get_cache_stats'>ResourceManager.get_cache_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t46">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t46"><data value='get_array'>SharedBuffer.get_array</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t50">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t50"><data value='get_memoryview'>SharedBuffer.get_memoryview</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t54">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t54"><data value='cleanup'>SharedBuffer.cleanup</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t69">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t69"><data value='init__'>SharedMemoryManager.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t89">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t89"><data value='create_shared_buffer'>SharedMemoryManager.create_shared_buffer</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t175">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t175"><data value='get_shared_buffer'>SharedMemoryManager.get_shared_buffer</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t180">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t180"><data value='create_buffer_for_cpp'>SharedMemoryManager.create_buffer_for_cpp</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t193">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t193"><data value='expose_numpy_buffer_to_cpp'>SharedMemoryManager.expose_numpy_buffer_to_cpp</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t209">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t209"><data value='optimize_for_process_pool'>SharedMemoryManager.optimize_for_process_pool</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t237">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t237"><data value='get_array_from_buffer_name'>SharedMemoryManager.get_array_from_buffer_name</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t244">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t244"><data value='cleanup_buffer'>SharedMemoryManager.cleanup_buffer</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t253">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t253"><data value='cleanup_all_buffers'>SharedMemoryManager.cleanup_all_buffers</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t263">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t263"><data value='get_buffer_stats'>SharedMemoryManager.get_buffer_stats</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t273">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t273"><data value='temporary_shared_buffer'>SharedMemoryManager.temporary_shared_buffer</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t285">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t285"><data value='init__'>ZeroCopyKernelInterface.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t289">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t289"><data value='call_kernel_with_zero_copy'>ZeroCopyKernelInterface.call_kernel_with_zero_copy</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t340">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t340"><data value='cleanup_shared_memory'>cleanup_shared_memory</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>54</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="51 54">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t92">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t92"><data value='init__'>SignalManager.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t104">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t104"><data value='setup_logging'>SignalManager._setup_logging</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t120">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t120"><data value='connect_threadsafe'>SignalManager.connect_threadsafe</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t147">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t147"><data value='disconnect_all'>SignalManager.disconnect_all</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t163">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t163"><data value='get_connection_count'>SignalManager.get_connection_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t167">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t167"><data value='emit_with_error_handling'>SignalManager.emit_with_error_handling</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t187">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t187"><data value='typed_slot'>TypedSlotDecorator.typed_slot</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t197">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t197"><data value='decorator'>TypedSlotDecorator.typed_slot.decorator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t198">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t198"><data value='wrapper'>TypedSlotDecorator.typed_slot.decorator.wrapper</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t31">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t31"><data value='init__'>VisualizationService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t36">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t36"><data value='shutdown'>VisualizationService.shutdown</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t40">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t40"><data value='prepare_candlestick_data'>VisualizationService.prepare_candlestick_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t69">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t69"><data value='prepare_rebased_candlestick_data'>VisualizationService.prepare_rebased_candlestick_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t96">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t96"><data value='prepare_peak_trough_display_data'>VisualizationService.prepare_peak_trough_display_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t120">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t120"><data value='calculate_standard_deviations'>VisualizationService.calculate_standard_deviations</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t149">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t149"><data value='prepare_vector_plot_data'>VisualizationService.prepare_vector_plot_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t171">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t171"><data value='calculate_crosshair_info'>VisualizationService.calculate_crosshair_info</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t197">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t197"><data value='determine_donchian_color'>VisualizationService.determine_donchian_color</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t217">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t217"><data value='prepare_donchian_stairstep_data'>VisualizationService.prepare_donchian_stairstep_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t241">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t241"><data value='prepare_rebased_vector_stairstep_data'>VisualizationService.prepare_rebased_vector_stairstep_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t265">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t265"><data value='calculate_mean_peak_trough_levels'>VisualizationService.calculate_mean_peak_trough_levels</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t292">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t292"><data value='prepare_predictive_cycle_lines'>VisualizationService.prepare_predictive_cycle_lines</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t317">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t317"><data value='prepare_all_visualization_data'>prepare_all_visualization_data</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_worker_py.html#t24">src\backend\visualization_worker.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_worker_py.html#t24"><data value='prepare_visualization_data_worker'>prepare_visualization_data_worker</data></a></td>
                <td>121</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="0 121">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_worker_py.html">src\backend\visualization_worker.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_worker_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t14">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t14"><data value='init__'>VolatilityCalculationsService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t18">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t18"><data value='calculate_volatility_data'>VolatilityCalculationsService.calculate_volatility_data</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t93">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t93"><data value='calculate_title_info'>VolatilityCalculationsService._calculate_title_info</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t124">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t124"><data value='extract_current_close'>VolatilityCalculationsService._extract_current_close</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t140">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t140"><data value='extract_highs_data'>VolatilityCalculationsService._extract_highs_data</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t153">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t153"><data value='extract_lows_data'>VolatilityCalculationsService._extract_lows_data</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t166">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t166"><data value='calculate_apex_values'>VolatilityCalculationsService._calculate_apex_values</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t176">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t176"><data value='calculate_highs_statistics'>VolatilityCalculationsService._calculate_highs_statistics</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t186">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t186"><data value='calculate_lows_statistics'>VolatilityCalculationsService._calculate_lows_statistics</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t196">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t196"><data value='calculate_arrow_sizes'>VolatilityCalculationsService._calculate_arrow_sizes</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t204">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t204"><data value='calculate_axis_limits'>VolatilityCalculationsService._calculate_axis_limits</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t214">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t214"><data value='calculate_title_position'>VolatilityCalculationsService._calculate_title_position</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>3551</td>
                <td>3231</td>
                <td>5</td>
                <td class="right" data-ratio="320 3551">9%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.4">coverage.py v7.10.4</a>,
            created at 2025-08-25 22:53 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
