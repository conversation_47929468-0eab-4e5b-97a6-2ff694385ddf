<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">9%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.4">coverage.py v7.10.4</a>,
            created at 2025-08-25 22:53 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7___init___py.html">src\backend\__init__.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t32">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t32"><data value='BufferProtocol'>BufferProtocol</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t39">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t39"><data value='ZeroCopyBufferManager'>ZeroCopyBufferManager</data></a></td>
                <td>73</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="10 73">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t142">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html#t142"><data value='MockResult'>ZeroCopyBufferManager.call_cpp_kernel_with_optimal_buffers.MockResult</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html">src\backend\buffer_utils.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_buffer_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>8</td>
                <td>3</td>
                <td class="right" data-ratio="22 30">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t12">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t12"><data value='ChartDataModel'>ChartDataModel</data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t147">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t147"><data value='CandlestickRenderItem'>CandlestickRenderItem</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t191">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html#t191"><data value='BandwidthChartModel'>BandwidthChartModel</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html">src\backend\chart_data_models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_chart_data_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t14">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t14"><data value='ComputationType'>ComputationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t26">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t26"><data value='ComputationTask'>ComputationTask</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t36">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t36"><data value='ComputationWorker'>ComputationWorker</data></a></td>
                <td>120</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="0 120">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t287">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t287"><data value='ComputationSignals'>ComputationSignals</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t297">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html#t297"><data value='ComputationOffloader'>ComputationOffloader</data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html">src\backend\computation_offloader.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_computation_offloader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t49">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html#t49"><data value='DataService'>DataService</data></a></td>
                <td>780</td>
                <td>780</td>
                <td>0</td>
                <td class="right" data-ratio="0 780">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html">src\backend\data_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_data_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="72 75">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t11">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html#t11"><data value='DensityCalculationsService'>DensityCalculationsService</data></a></td>
                <td>429</td>
                <td>429</td>
                <td>0</td>
                <td class="right" data-ratio="0 429">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html">src\backend\density_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_density_calculations_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t21">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t21"><data value='MetricDefinition'>MetricDefinition</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t34">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t34"><data value='PrometheusMetricsExporter'>PrometheusMetricsExporter</data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t237">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html#t237"><data value='MetricsServer'>MetricsServer</data></a></td>
                <td>72</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html">src\backend\metrics_server.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_metrics_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t22">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t22"><data value='ChartElementType'>ChartElementType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t33">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t33"><data value='ChartElement'>ChartElement</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t47">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html#t47"><data value='ChartDataModel'>ChartDataModel</data></a></td>
                <td>140</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="0 140">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html">src\backend\models.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t19">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t19"><data value='CorrelationContext'>CorrelationContext</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t35">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t35"><data value='CorrelationManager'>CorrelationManager</data></a></td>
                <td>23</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="4 23">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t97">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t97"><data value='MetricsCollector'>MetricsCollector</data></a></td>
                <td>46</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="7 46">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t212">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t212"><data value='StructuredLogger'>StructuredLogger</data></a></td>
                <td>20</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="16 20">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t277">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html#t277"><data value='StructuredFormatter'>StructuredFormatter</data></a></td>
                <td>17</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="13 17">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html">src\backend\observability.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_observability_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>67</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="54 67">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t16">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html#t16"><data value='OptionsService'>OptionsService</data></a></td>
                <td>277</td>
                <td>277</td>
                <td>0</td>
                <td class="right" data-ratio="0 277">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html">src\backend\options_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_options_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_rebasing_py.html">src\backend\rebasing.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_rebasing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>110</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t15">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html#t15"><data value='ResourceManager'>ResourceManager</data></a></td>
                <td>156</td>
                <td>156</td>
                <td>0</td>
                <td class="right" data-ratio="0 156">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html">src\backend\resource_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_resource_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t37">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t37"><data value='SharedBuffer'>SharedBuffer</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t63">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t63"><data value='SharedMemoryManager'>SharedMemoryManager</data></a></td>
                <td>82</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="11 82">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t282">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html#t282"><data value='ZeroCopyKernelInterface'>ZeroCopyKernelInterface</data></a></td>
                <td>24</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="2 24">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html">src\backend\shared_memory_manager.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_shared_memory_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="51 55">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t11">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t11"><data value='MarketDataSignals'>MarketDataSignals</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t38">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t38"><data value='ChartVisualizationSignals'>ChartVisualizationSignals</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t63">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t63"><data value='BackendServiceSignals'>BackendServiceSignals</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t86">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t86"><data value='SignalManager'>SignalManager</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t180">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html#t180"><data value='TypedSlotDecorator'>TypedSlotDecorator</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html">src\backend\signals.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_signals_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t28">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html#t28"><data value='VisualizationService'>VisualizationService</data></a></td>
                <td>113</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="0 113">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html">src\backend\visualization_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_worker_py.html">src\backend\visualization_worker.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_visualization_worker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>134</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="0 134">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t11">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html#t11"><data value='VolatilityCalculationsService'>VolatilityCalculationsService</data></a></td>
                <td>107</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="0 107">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html">src\backend\volatility_calculations_service.py</a></td>
                <td class="name left"><a href="z_798a2b81d00ca5b7_volatility_calculations_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>3551</td>
                <td>3231</td>
                <td>5</td>
                <td class="right" data-ratio="320 3551">9%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.4">coverage.py v7.10.4</a>,
            created at 2025-08-25 22:53 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
